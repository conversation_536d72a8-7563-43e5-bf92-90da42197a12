# I18n Service

一个高性能的多语言国际化服务，为 Java 微服务提供翻译配置 API。使用 Go + Fiber 框架构建，集成 Prometheus 监控和缓存机制。

## 功能特性

- 🚀 **高性能**: 基于 Fiber 框架，支持高并发请求
- 📊 **监控集成**: 内置 Prometheus 指标监控
- 💾 **智能缓存**: 内存缓存机制，提升响应速度
- 🔒 **限流保护**: API 请求频率限制
- 🐳 **容器化**: Docker 支持，易于部署
- 🔄 **版本控制**: 支持多环境版本管理
- 📝 **完整日志**: 结构化日志记录

## 架构设计

```
Application → Environment → Namespace → TranslationKey
```

- **Application**: 微服务应用
- **Environment**: 部署环境 (DEV/TEST/pre/PROD)
- **Namespace**: 翻译命名空间
- **TranslationKey**: 具体的翻译键值对

## API 接口

### 核心翻译接口

```bash
# 获取命名空间下所有翻译
GET /api/v1/translations/:namespace

# 获取特定翻译键
GET /api/v1/translations/:namespace/:key

# 获取所有翻译（支持过滤）
GET /api/v1/translations?application_id=xxx&namespaces=ns1,ns2

# 获取环境下的翻译
GET /api/v1/environments/:env/translations/:namespace
```

### 环境管理接口

```bash
# 获取所有环境
GET /api/v1/environments
```

### 缓存管理接口

```bash
# 清空所有缓存
DELETE /api/v1/cache

# 清空特定命名空间缓存
DELETE /api/v1/cache/:namespace

# 获取缓存统计
GET /api/v1/cache/stats
```

### 健康检查

```bash
# 服务健康检查
GET /health

# Prometheus 指标
GET /metrics
```

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Docker & Docker Compose (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository>
cd i18n-service
```

2. **安装依赖**
```bash
make deps
```

3. **配置环境变量**
```bash
cp config.env.example .env
# 编辑 .env 文件，配置数据库连接等
```

4. **运行服务**
```bash
make run
```

5. **开发模式（热重载）**
```bash
make dev  # 需要先安装 air: go install github.com/cosmtrek/air@latest
```

### Docker 部署

1. **使用 Docker Compose**
```bash
make docker-run
```

这将启动以下服务：
- I18n Service (端口 8080)
- MySQL 数据库 (端口 3306)
- Prometheus (端口 9091)
- Grafana (端口 3001)

2. **查看日志**
```bash
make docker-logs
```

3. **停止服务**
```bash
make docker-stop
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DB_HOST` | localhost | 数据库主机 |
| `DB_PORT` | 3306 | 数据库端口 |
| `DB_USER` | root | 数据库用户名 |
| `DB_PASSWORD` | | 数据库密码 |
| `DB_NAME` | i18n | 数据库名称 |
| `PORT` | 8080 | 服务端口 |
| `METRICS_PORT` | 9090 | 指标端口 |
| `ENV` | development | 运行环境 |
| `CACHE_TTL` | 3600 | 缓存过期时间（秒） |
| `API_VERSION` | v1 | API 版本 |
| `MAX_REQUESTS_PER_MINUTE` | 1000 | 每分钟最大请求数 |

## 监控指标

服务提供以下 Prometheus 指标：

### HTTP 指标
- `http_requests_total`: HTTP 请求总数
- `http_request_duration_seconds`: HTTP 请求耗时

### 翻译指标
- `translation_requests_total`: 翻译请求总数
- `cache_hit_ratio`: 缓存命中率

### 系统指标
- `active_connections`: 活跃连接数
- `database_connections_active`: 数据库连接数
- `database_query_duration_seconds`: 数据库查询耗时

## 性能测试

### 负载测试
```bash
make load-test
```

### 基准测试
```bash
make benchmark-translations
```

## API 使用示例

### 获取翻译数据

```bash
# 获取 common 命名空间下的所有翻译
curl http://localhost:8080/api/v1/translations/common

# 获取生产环境下的翻译
curl http://localhost:8080/api/v1/environments/PROD/translations/common

# 获取特定翻译键
curl http://localhost:8080/api/v1/translations/common/hello
```

### 响应格式

```json
{
  "namespace": "common",
  "environment": "PROD",
  "version": "1.0.0",
  "translations": {
    "hello.en": "Hello",
    "hello.zh": "你好",
    "goodbye.en": "Goodbye",
    "goodbye.zh": "再见"
  },
  "metadata": {
    "count": 4,
    "lastUpdated": "2024-01-01T12:00:00Z",
    "cacheHit": true
  }
}
```

## 开发工具

### 安装开发工具
```bash
make install-tools
```

### 代码格式化
```bash
make fmt
```

### 代码检查
```bash
make lint
```

### 运行测试
```bash
make test
make test-coverage
```

## 项目结构

```
i18n-service/
├── cmd/server/          # 应用入口
├── internal/
│   ├── config/         # 配置管理
│   ├── database/       # 数据库连接
│   ├── handler/        # HTTP 处理器
│   ├── middleware/     # 中间件
│   ├── model/          # 数据模型
│   ├── repository/     # 数据访问层
│   └── service/        # 业务逻辑层
├── docker-compose.yml  # Docker 编排
├── Dockerfile         # Docker 镜像
├── Makefile          # 构建脚本
└── README.md         # 项目文档
```

## 部署建议

### 生产环境

1. **资源配置**
   - CPU: 2 核心以上
   - 内存: 4GB 以上
   - 磁盘: SSD 推荐

2. **数据库优化**
   - 启用查询缓存
   - 配置适当的连接池大小
   - 定期备份数据

3. **监控告警**
   - 配置 Prometheus 告警规则
   - 监控关键指标：响应时间、错误率、缓存命中率
   - 设置日志聚合和分析

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和网络连通性
   - 确认数据库用户权限

2. **缓存命中率低**
   - 检查缓存配置
   - 分析访问模式，调整缓存策略

3. **响应时间过长**
   - 检查数据库查询性能
   - 分析慢查询日志
   - 考虑增加缓存或优化查询

### 日志查看

```bash
# Docker 环境
docker-compose logs -f i18n-service

# 本地环境
tail -f logs/app.log
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
