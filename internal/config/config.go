package config

import (
	"encoding/json"
	"go.uber.org/zap"
	"os"

	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
)

// DBConfig 数据库配置结构
type DBConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"dbname"`
}

type RedisConfig struct {
	Host     string   `json:"host"`
	Port     int      `json:"port"`
	Password string   `json:"password"`
	DB       int      `json:"db"`
	Enabled  bool     `json:"enabled"` // Redis 缓存开关
	Cluster  bool     `json:"cluster"` // 是否使用集群模式
	Addrs    []string `json:"addrs"`   // 集群地址列表
	Prefix   string   `json:"prefix"`  // 缓存键前缀
}

type Config struct {
	DB       DBConfig
	Redis    RedisConfig
	HTTPPort string
}

var (
	globalConfig *Config
)

func InitConfig(apolloCfg *apollo.ApolloConfig) error {
	// 初始化 zap logger
	apollo.Init(apolloCfg)
	// 初始加载配置
	return reloadConfig()
}

func reloadConfig() error {
	cfg := &Config{}
	// 从 JSON 解析 media 数据库配置
	i18nDBJSON := apollo.GetStringValue("db.i18n", "")
	if i18nDBJSON != "" {
		var dbConfig DBConfig
		if err := json.Unmarshal([]byte(i18nDBJSON), &dbConfig); err != nil {
			logger.GetConsoleLogger().Error("Failed to parse comment db config", zap.Error(err))
			return err
		}
		cfg.DB = dbConfig
	}

	// Redis 配置
	redisJSON := apollo.GetStringValue("redis.i18n", "")
	if redisJSON != "" {
		var redisConfig RedisConfig
		if err := json.Unmarshal([]byte(redisJSON), &redisConfig); err != nil {
			logger.GetConsoleLogger().Error("Failed to parse media redis config", zap.Error(err))
			return err
		}
		cfg.Redis = redisConfig
	}

	cfg.HTTPPort = apollo.GetStringValue("http.port", "8080")
	if os.Getenv("SERVER_LISTEN_PORT") != "" {
		cfg.HTTPPort = os.Getenv("SERVER_LISTEN_PORT")
	}

	globalConfig = cfg
	logger.GetConsoleLogger().Info("Config reloaded successfully")
	return nil
}

func GetConfig() *Config {
	return globalConfig
}
