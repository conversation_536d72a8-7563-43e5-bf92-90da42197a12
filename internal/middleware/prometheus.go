package middleware

import (
	"strconv"
	"time"

	"github.com/XmePlatform/i18n-service/internal/pkg/metrics"

	"github.com/XmePlatform/i18n-service/internal/utils"
	"github.com/gofiber/fiber/v2"
)

// PrometheusMiddleware 记录请求指标的中间件
func PrometheusMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()
		path := utils.GetCopiedPath(c)
		method := utils.GetCopiedMethod(c)
		if path == "" {
			path = "unknown"
		}
		if utils.IsMetricsURL(path) {
			return c.Next()
		}

		// 增加正在处理的请求数
		metrics.IncrementHTTPRequestsInFlight()
		defer metrics.DecrementHTTPRequestsInFlight()

		// 处理请求
		err := c.Next()

		// 记录请求状态和耗时
		status := strconv.Itoa(c.Response().StatusCode())
		duration := time.Since(start).Seconds()

		metrics.RecordHTTPRequest(status, method, path)
		metrics.RecordHTTPRequestDuration(status, method, path, duration)

		return err
	}
}
