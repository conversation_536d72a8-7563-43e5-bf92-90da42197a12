package middleware

import (
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/XmePlatform/i18n-service/internal/pkg/metrics"
	"github.com/XmePlatform/i18n-service/internal/utils"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

// 响应监控中间件的上下文键
const (
	ContextKeyResponseCode    = "response_code"
	ContextKeyResponseSuccess = "response_success"
)

// AvailabilityMiddleware 服务可用性监控中间件
func AvailabilityMiddleware(logger *logger.AppLogger) fiber.Handler {
	return func(c *fiber.Ctx) error {
		path := utils.GetCopiedPath(c)
		method := utils.GetCopiedMethod(c)
		if utils.IsMetricsURL(path) {
			return c.Next()
		}
		// 处理请求
		err := c.Next()

		// 获取响应信息
		var code int = 0
		var success bool = true

		// 从 c.Locals 中获取响应码和成功状态
		if val := c.Locals(ContextKeyResponseCode); val != nil {
			if codeVal, ok := val.(int); ok {
				code = codeVal
			}
		}

		if val := c.Locals(ContextKeyResponseSuccess); val != nil {
			if successVal, ok := val.(bool); ok {
				success = successVal
			}
		}

		// 如果没有捕获到自定义响应码，则使用 HTTP 状态码
		if code == 0 {
			code = c.Response().StatusCode()
			// 假设非 2xx 状态码表示失败
			success = code >= 200 && code < 300
		}

		// 调试日志
		logger.Debug(c.Context(), "Response metrics",
			zap.Int("code", code),
			zap.Bool("success", success),
			zap.String("path", path),
			zap.String("method", method))

		// 报告请求状态
		metrics.ReportRequestStatus(path, method, code, success)

		return err
	}
}
