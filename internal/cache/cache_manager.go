package cache

import (
	"fmt"
	"time"

	"github.com/XmePlatform/i18n-service/internal/config"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/patrickmn/go-cache"
	"go.uber.org/zap"
)

// CacheManager 缓存管理器
type CacheManager struct {
	cache CacheInterface
}

var globalCacheManager *CacheManager

// InitCache 初始化缓存
func InitCache(cfg *config.Config) error {
	var cacheImpl CacheInterface

	if cfg.Redis.Enabled {
		logger.GetConsoleLogger().Info("Initializing Redis cache",
			zap.Bool("cluster", cfg.Redis.Cluster),
			zap.String("prefix", cfg.Redis.Prefix))

		if cfg.Redis.Cluster {
			// 使用集群模式
			if len(cfg.Redis.Addrs) == 0 {
				return fmt.Errorf("redis cluster addresses not configured")
			}
			cacheImpl = NewRedisClusterCache(cfg.Redis.Addrs, cfg.Redis.Password, cfg.Redis.Prefix)
		} else {
			// 使用单机模式
			addr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
			cacheImpl = NewRedisCache(addr, cfg.Redis.Password, cfg.Redis.DB, cfg.Redis.Prefix)
		}

		if cacheImpl == nil {
			logger.GetConsoleLogger().Warn("Failed to initialize Redis cache, falling back to memory cache")
			cacheImpl = NewMemoryCache(5*time.Minute, 10*time.Minute)
		}
	} else {
		logger.GetConsoleLogger().Info("Using memory cache")
		cacheImpl = NewMemoryCache(5*time.Minute, 10*time.Minute)
	}

	globalCacheManager = &CacheManager{
		cache: cacheImpl,
	}

	logger.GetConsoleLogger().Info("Cache initialized successfully")
	return nil
}

// GetCacheManager 获取全局缓存管理器
func GetCacheManager() *CacheManager {
	return globalCacheManager
}

// Set 设置缓存
func (cm *CacheManager) Set(key string, value interface{}, expiration time.Duration) error {
	return cm.cache.Set(key, value, expiration)
}

// Get 获取缓存
func (cm *CacheManager) Get(key string, dest interface{}) error {
	return cm.cache.Get(key, dest)
}

// Delete 删除缓存
func (cm *CacheManager) Delete(key string) error {
	return cm.cache.Delete(key)
}

// DeleteByPrefix 根据前缀删除缓存（已废弃，为了兼容性保留）
func (cm *CacheManager) DeleteByPrefix(prefix string) error {
	return cm.cache.DeleteByPrefix(prefix)
}

// DeleteSpecificKeys 删除指定的缓存键列表（高性能版本）
func (cm *CacheManager) DeleteSpecificKeys(keys []string) error {
	if redisCache, ok := cm.cache.(*RedisCache); ok {
		return redisCache.DeleteSpecificKeys(keys)
	}
	// 对于内存缓存，逐个删除
	for _, key := range keys {
		if err := cm.cache.Delete(key); err != nil {
			return err
		}
	}
	return nil
}

// Items 获取所有缓存项（仅用于调试和监控）
func (cm *CacheManager) Items() map[string]interface{} {
	return cm.cache.Items()
}

// Close 关闭缓存
func (cm *CacheManager) Close() error {
	if cm.cache != nil {
		return cm.cache.Close()
	}
	return nil
}

// MemoryCache 内存缓存实现（作为备用）
type MemoryCache struct {
	cache *cache.Cache
}

// NewMemoryCache 创建内存缓存实例
func NewMemoryCache(defaultExpiration, cleanupInterval time.Duration) *MemoryCache {
	return &MemoryCache{
		cache: cache.New(defaultExpiration, cleanupInterval),
	}
}

// Set 设置缓存
func (m *MemoryCache) Set(key string, value interface{}, expiration time.Duration) error {
	m.cache.Set(key, value, expiration)
	return nil
}

// Get 获取缓存
func (m *MemoryCache) Get(key string, dest interface{}) error {
	value, found := m.cache.Get(key)
	if !found {
		return fmt.Errorf("cache miss")
	}

	// 简单的类型断言，实际使用中可能需要更复杂的转换
	switch d := dest.(type) {
	case *interface{}:
		*d = value
	case *string:
		if str, ok := value.(string); ok {
			*d = str
		} else {
			return fmt.Errorf("type assertion failed")
		}
	case *[]interface{}:
		if slice, ok := value.([]interface{}); ok {
			*d = slice
		} else {
			return fmt.Errorf("type assertion failed")
		}
	default:
		return fmt.Errorf("unsupported destination type")
	}

	return nil
}

// Delete 删除缓存
func (m *MemoryCache) Delete(key string) error {
	m.cache.Delete(key)
	return nil
}

// DeleteByPrefix 根据前缀删除缓存
func (m *MemoryCache) DeleteByPrefix(prefix string) error {
	items := m.cache.Items()
	for key := range items {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			m.cache.Delete(key)
		}
	}
	return nil
}

// Flush 清空所有缓存
func (m *MemoryCache) Flush() error {
	m.cache.Flush()
	return nil
}

// Items 获取所有缓存项
func (m *MemoryCache) Items() map[string]interface{} {
	items := m.cache.Items()
	result := make(map[string]interface{})
	for key, item := range items {
		result[key] = item.Object
	}
	return result
}

// Close 关闭缓存
func (m *MemoryCache) Close() error {
	return nil
}

// 确保MemoryCache实现了CacheInterface接口
var _ CacheInterface = (*MemoryCache)(nil)
