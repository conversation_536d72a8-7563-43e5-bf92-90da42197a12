package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// CacheInterface 定义缓存接口
type CacheInterface interface {
	Set(key string, value interface{}, expiration time.Duration) error
	Get(key string, dest interface{}) error
	Delete(key string) error
	DeleteByPrefix(prefix string) error
	Flush() error
	Items() map[string]interface{}
	Close() error
}

// RedisCache Redis缓存实现
type RedisCache struct {
	client redis.Cmdable // 使用接口类型，支持单机和集群
	prefix string
	ctx    context.Context
}

// NewRedisCache 创建Redis缓存实例（单机版）
func NewRedisCache(addr, password string, db int, prefix string) *RedisCache {
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	if prefix == "" {
		prefix = "i18n"
	}
	ctx := context.Background()

	// 测试连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to connect to Redis", zap.Error(err))
		return nil
	}

	logger.GetConsoleLogger().Info("Connected to Redis successfully",
		zap.String("addr", addr),
		zap.Int("db", db))

	return &RedisCache{
		client: rdb,
		prefix: prefix,
		ctx:    ctx,
	}
}

// NewRedisClusterCache 创建Redis集群缓存实例
func NewRedisClusterCache(addrs []string, password string, prefix string) *RedisCache {
	rdb := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    addrs,
		Password: password,
	})

	ctx := context.Background()
	if prefix == "" {
		prefix = "i18n"
	}
	// 测试连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to connect to Redis Cluster", zap.Error(err))
		return nil
	}

	logger.GetConsoleLogger().Info("Connected to Redis Cluster successfully",
		zap.Strings("addrs", addrs))

	return &RedisCache{
		client: rdb,
		prefix: prefix,
		ctx:    ctx,
	}
}

// Set 设置缓存
func (r *RedisCache) Set(key string, value interface{}, expiration time.Duration) error {
	fullKey := r.getFullKey(key)

	// 序列化值
	data, err := json.Marshal(value)
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to marshal cache value",
			zap.String("key", fullKey),
			zap.Error(err))
		return err
	}

	// 设置到Redis
	err = r.client.Set(r.ctx, fullKey, data, expiration).Err()
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to set cache",
			zap.String("key", fullKey),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Debug("Cache set successfully",
		zap.String("key", fullKey),
		zap.Duration("expiration", expiration))
	return nil
}

// Get 获取缓存
func (r *RedisCache) Get(key string, dest interface{}) error {
	fullKey := r.getFullKey(key)

	data, err := r.client.Get(r.ctx, fullKey).Result()
	if err == redis.Nil {
		logger.GetConsoleLogger().Debug("Cache miss", zap.String("key", fullKey))
		return fmt.Errorf("cache miss")
	}
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to get cache",
			zap.String("key", fullKey),
			zap.Error(err))
		return err
	}

	// 反序列化到目标对象
	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to unmarshal cache value",
			zap.String("key", fullKey),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Debug("Cache hit", zap.String("key", fullKey))
	return nil
}

// Delete 删除缓存
func (r *RedisCache) Delete(key string) error {
	fullKey := r.getFullKey(key)

	err := r.client.Del(r.ctx, fullKey).Err()
	if err != nil {
		logger.GetConsoleLogger().Error("Failed to delete cache",
			zap.String("key", fullKey),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Debug("Cache deleted", zap.String("key", fullKey))
	return nil
}

// DeleteByPrefix 根据前缀删除缓存（已废弃，为了兼容性保留）
func (r *RedisCache) DeleteByPrefix(prefix string) error {
	logger.GetConsoleLogger().Warn("DeleteByPrefix is deprecated for performance reasons")
	return nil
}

// DeleteSpecificKeys 删除指定的缓存键列表（Redis集群兼容版本）
func (r *RedisCache) DeleteSpecificKeys(keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	// 为了避免Redis集群的CROSSSLOT错误，逐个删除键
	// 在Redis集群中，不同的键可能散列到不同的槽位，批量操作会失败
	var deletedCount int
	var lastError error

	for _, key := range keys {
		fullKey := r.getFullKey(key)
		err := r.client.Del(r.ctx, fullKey).Err()
		if err != nil {
			logger.GetConsoleLogger().Warn("Failed to delete specific key",
				zap.String("key", fullKey),
				zap.Error(err))
			lastError = err
		} else {
			deletedCount++
		}
	}

	if lastError != nil && deletedCount == 0 {
		// 如果所有键都删除失败，返回最后一个错误
		logger.GetConsoleLogger().Error("Failed to delete all specific keys",
			zap.Strings("keys", keys),
			zap.Error(lastError))
		return lastError
	}

	logger.GetConsoleLogger().Debug("Specific keys deleted",
		zap.Strings("keys", keys),
		zap.Int("deleted_count", deletedCount),
		zap.Int("total_count", len(keys)))

	return nil
}

// Flush 清空所有缓存
func (r *RedisCache) Flush() error {
	logger.GetConsoleLogger().Warn("Flush is deprecated for performance reasons")
	return nil
}

// Items 获取所有缓存项（用于兼容内存缓存接口）
func (r *RedisCache) Items() map[string]interface{} {
	items := make(map[string]interface{})
	logger.GetConsoleLogger().Warn("Items is deprecated for performance reasons")

	return items
}

// Close 关闭Redis连接
func (r *RedisCache) Close() error {
	if closer, ok := r.client.(interface{ Close() error }); ok {
		return closer.Close()
	}
	return nil
}

// getFullKey 获取完整的缓存键
func (r *RedisCache) getFullKey(key string) string {
	if r.prefix == "" {
		return key
	}
	return fmt.Sprintf("%s:%s", r.prefix, key)
}

// stripPrefix 移除前缀
func (r *RedisCache) stripPrefix(fullKey string) string {
	if r.prefix == "" {
		return fullKey
	}
	prefix := r.prefix + ":"
	return strings.TrimPrefix(fullKey, prefix)
}

// 确保RedisCache实现了CacheInterface接口
var _ CacheInterface = (*RedisCache)(nil)
