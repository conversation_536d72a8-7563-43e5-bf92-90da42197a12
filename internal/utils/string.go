package utils

import (
	"golang.org/x/net/html"
	"regexp"
	"strconv"
	"strings"
)

// Function to remove HTML tags from a string
func RemoveHTMLTags(input string) string {
	re := regexp.MustCompile(`<.*?>`)
	return re.ReplaceAllString(input, "")
}
func GetIntValue(s string) int {
	i, _ := strconv.Atoi(s)
	return i
}

func StripHTML(input string) (string, error) {
	// 创建 HTML tokenizer
	tokenizer := html.NewTokenizer(strings.NewReader(input))

	var output strings.Builder

	// 添加一个标志来跟踪是否在 style 标签内
	inStyle := false

	for {
		// 获取下一个token类型
		tokenType := tokenizer.Next()

		// 检查是否到达文档末尾或发生错误
		if tokenType == html.ErrorToken {
			err := tokenizer.Err()
			if err.Error() == "EOF" {
				break
			}
			return "", err
		}

		// 检查是否进入或离开 style 标签
		if tokenType == html.StartTagToken {
			tag, _ := tokenizer.TagName()
			if string(tag) == "style" {
				inStyle = true
				continue
			}
		} else if tokenType == html.EndTagToken {
			tag, _ := tokenizer.TagName()
			if string(tag) == "style" {
				inStyle = false
				continue
			}
		}

		// 只有在不在 style 标签内时才添加文本
		if !inStyle && tokenType == html.TextToken {
			text := strings.TrimSpace(string(tokenizer.Text()))
			if text != "" {
				output.WriteString(text)
				output.WriteString(" ") // 添加空格分隔文本
			}
		}
	}

	return strings.TrimSpace(output.String()), nil
}

func GetInt64Value(s string) int64 {
	i, _ := strconv.ParseInt(s, 10, 64)
	return i
}

func GetUInt64Value(s string) uint64 {
	i, _ := strconv.ParseUint(s, 10, 64)
	return i
}

func IsMetricsURL(path string) bool {
	if path == "/" || path == "/metrics" || path == "/health" {
		return true
	}
	return false
}
