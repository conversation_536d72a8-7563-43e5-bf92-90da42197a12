package utils

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// GracefullyShutdown 服务的优雅启停
func GracefullyShutdown(work func()) {
	c := make(chan os.Signal, 1)

	// The only signal values guaranteed to be present in the os package on all
	// systems are os.Interrupt (send the process an interrupt) and os.Kill (force
	// the process to exit).
	//
	// BUT:
	//
	// if we deploy app in docker, there will be little different.
	// Here is what docker stop do:
	// send SIGTERM to PID=1, and wait 10s(default) and if pid still live then send SIGKILL
	// Notice: SIGKILL is direct send to kernel, application has no chance to handle it.
	// So we need watch SIGTERM.
	signal.Notify(c, os.Interrupt, os.Kill, syscall.SIGTERM)
	onStop(c, 3*time.Second, work)
}

func onStop(c chan os.Signal, timeout time.Duration, work func()) {
	select {
	case <-c:
		log.Printf("service stop begin, time=%v", time.Now())
		go func() {
			if _, ok := <-time.After(timeout); ok {
				log.Printf("service stop timeout, force exist, time=%v\n", time.Now())
				os.Exit(-1)
			}
		}()
		work()
		log.Printf("service stop end, time=%v", time.Now())
	}
}
