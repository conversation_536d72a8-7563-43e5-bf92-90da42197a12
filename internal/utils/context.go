package utils

import (
	"context"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/utils"
	"github.com/google/uuid"
)

const (
	// ContextKeyRequestID 请求ID的上下文键
	ContextKeyRequestID = "request_id"
	ContextUserID       = "user_id"
	ContextLocale       = "locale"
)

// GetRequestID 获取请求ID，如果不存在则生成新的
func GetRequestID(c *fiber.Ctx) string {
	if requestID := c.Locals(ContextKeyRequestID); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return generateRequestID()
}

// SetRequestID 设置请求ID到上下文
func SetRequestID(c *fiber.Ctx) {
	requestID := c.Get("X-Request-ID")
	if requestID == "" {
		requestID = generateRequestID()
	}
	ctx := context.WithValue(c.UserContext(), ContextKeyRequestID, requestID)
	c.SetUserContext(ctx)
	c.Locals(ContextKeyRequestID, requestID)
	c.Set("X-Request-ID", requestID)
}

func GetLocale(ctx context.Context) string {
	val := ctx.Value(ContextLocale)
	if str, ok := val.(string); ok {
		return str
	}
	return "en_us"
}

func SetLocale(c *fiber.Ctx, locale string) {
	ctx := context.WithValue(c.UserContext(), ContextLocale, locale)
	c.SetUserContext(ctx)
	c.Locals(ContextLocale, locale)
}

func GetUserID(ctx context.Context) uint64 {
	val := ctx.Value(ContextUserID)
	if id, ok := val.(uint64); ok {
		return id
	}
	return 0
}

func SetUserID(c *fiber.Ctx, userID uint64) {
	ctx := context.WithValue(c.UserContext(), ContextUserID, userID)
	c.SetUserContext(ctx)
	c.Locals(ContextUserID, userID)
}

func IsLoginIn(ctx context.Context) bool {
	val := ctx.Value(ContextUserID)
	if _, ok := val.(uint64); ok {
		return true
	}
	return false
}

// generateRequestID 生成新的请求ID
func generateRequestID() string {
	return uuid.New().String()
}

func GetCopiedPath(c *fiber.Ctx) string {
	routePath := utils.CopyString(c.Route().Path)

	// If the route path is empty, use the current path
	if routePath == "/" {
		routePath = utils.CopyString(c.Path())
	}

	// Normalize the path
	if routePath != "" && routePath != "/" {
		routePath = normalizePath(routePath)
	}
	return routePath
}

func GetCopiedMethod(c *fiber.Ctx) string {
	return utils.CopyString(c.Method())
}

func normalizePath(routePath string) string {
	normalized := strings.TrimRight(routePath, "/")
	if normalized == "" {
		return "/"
	}
	return normalized
}

func GetDeviceID(c *fiber.Ctx) string {
	deviceID := c.Get("X-Device-Fingerprint")
	if deviceID == "" {
		deviceID = c.Get("X-Device-Id")
	}
	return deviceID
}

func GetIP(c *fiber.Ctx) string {
	ips := c.IPs()
	if len(ips) > 0 {
		return ips[0]
	}
	return ""
}
