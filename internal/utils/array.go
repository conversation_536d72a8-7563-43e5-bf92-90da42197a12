package utils

func ArrayUniqueInt64(arr []int64) []int64 {
	uniqueMap := make(map[int64]struct{})
	var distinctArr []int64

	for _, num := range arr {
		if _, exists := uniqueMap[num]; !exists {
			uniqueMap[num] = struct{}{}
			distinctArr = append(distinctArr, num)
		}
	}
	return distinctArr
}

func ArrayUniqueUInt64(arr []uint64) []uint64 {
	uniqueMap := make(map[uint64]struct{})
	var distinctArr []uint64

	for _, num := range arr {
		if _, exists := uniqueMap[num]; !exists {
			uniqueMap[num] = struct{}{}
			distinctArr = append(distinctArr, num)
		}
	}
	return distinctArr
}
