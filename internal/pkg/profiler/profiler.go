package profiler

import (
	"fmt"
	"os"
	"runtime"
	"runtime/pprof"
	"time"

	"go.uber.org/zap"
)

// Profiler 性能分析器
type Profiler struct {
	logger *zap.Logger
}

// NewProfiler 创建性能分析器
func NewProfiler(logger *zap.Logger) *Profiler {
	return &Profiler{
		logger: logger,
	}
}

// StartCPUProfile 开始 CPU 性能分析
func (p *Profiler) StartCPUProfile(filename string) error {
	f, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("could not create CPU profile: %v", err)
	}
	if err := pprof.StartCPUProfile(f); err != nil {
		f.Close()
		return fmt.Errorf("could not start CPU profile: %v", err)
	}
	return nil
}

// StopCPUProfile 停止 CPU 性能分析
func (p *Profiler) StopCPUProfile() {
	pprof.StopCPUProfile()
}

// WriteHeapProfile 写入堆内存分析
func (p *Profiler) WriteHeapProfile(filename string) error {
	f, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("could not create memory profile: %v", err)
	}
	defer f.Close()
	runtime.GC() // 在写入堆信息前获取最新的数据
	if err := pprof.WriteHeapProfile(f); err != nil {
		return fmt.Errorf("could not write memory profile: %v", err)
	}
	return nil
}

// StartRuntimeStats 开始收集运行时统计信息
func (p *Profiler) StartRuntimeStats(interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		var m runtime.MemStats
		for range ticker.C {
			runtime.ReadMemStats(&m)
			p.logger.Info("Runtime statistics",
				zap.Uint64("alloc", m.Alloc),
				zap.Uint64("total_alloc", m.TotalAlloc),
				zap.Uint64("sys", m.Sys),
				zap.Uint32("num_gc", m.NumGC),
				zap.Uint32("goroutines", uint32(runtime.NumGoroutine())),
			)
		}
	}()
}
