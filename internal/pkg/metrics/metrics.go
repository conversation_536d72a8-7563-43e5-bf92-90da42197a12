package metrics

import (
	"strconv"
	"strings"
	"sync"

	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
)

// 服务名常量
const (
	ServiceName = "i18n-service"
)

// 指标名称常量
const (
	// 评论相关指标
	MetricNameCommentCreated = "comment_created_total"
	MetricNameCommentDeleted = "comment_deleted_total"

	// 点赞相关指标
	MetricNameCommentLiked   = "comment_liked_total"
	MetricNameCommentUnliked = "comment_unliked_total"

	// 缓存相关指标
	MetricNameCacheHits   = "cache_hits_total"
	MetricNameCacheMisses = "cache_misses_total"

	// 请求延迟指标
	MetricNameRequestDuration     = "request_duration_seconds"
	MetricNameDBOperationDuration = "db_operation_duration_seconds"

	// 统计同步指标
	MetricNameStatsSyncTotal    = "stats_sync_total"
	MetricNameStatsSyncErrors   = "stats_sync_errors_total"
	MetricNameStatsSyncDuration = "stats_sync_duration_seconds"

	// HTTP请求指标
	MetricNameHttpRequestsTotal    = "http_requests_total"
	MetricNameHttpRequestDuration  = "http_request_duration_seconds"
	MetricNameHttpRequestsInFlight = "http_requests_in_flight"

	// 服务可用性指标
	MetricNameServiceAvailability = "service_availability_total"
	MetricNameErrorCodes          = "service_error_codes_total"
)

// Prometheus 注册表
var (
	registry *prometheus.Registry

	// 标记 Prometheus 是否已初始化
	PrometheusInited bool = false
	syncOnce         sync.Once

	// 评论相关指标
	commentCreated *prometheus.CounterVec
	commentDeleted *prometheus.CounterVec

	// 点赞相关指标
	commentLiked   *prometheus.CounterVec
	commentUnliked *prometheus.CounterVec

	// 缓存相关指标
	cacheHits   *prometheus.CounterVec
	cacheMisses *prometheus.CounterVec

	// 请求延迟指标
	requestDuration     *prometheus.HistogramVec
	dbOperationDuration *prometheus.HistogramVec

	// 统计同步指标
	statsSyncTotal    *prometheus.CounterVec
	statsSyncErrors   *prometheus.CounterVec
	statsSyncDuration *prometheus.HistogramVec

	// HTTP请求指标
	httpRequestsTotal    *prometheus.CounterVec
	httpRequestDuration  *prometheus.HistogramVec
	httpRequestsInFlight prometheus.Gauge

	// 服务可用性指标
	serviceAvailability *prometheus.CounterVec
	errorCodeCounter    *prometheus.CounterVec

	// 默认排除的错误码
	defaultExcludedCodes = []int{
		400, // 参数错误
		404, // 资源不存在
	}

	// 当前排除的错误码
	excludedCodes     = defaultExcludedCodes
	excludedCodesLock sync.RWMutex

	// 排除的路径
	excludedPaths     = make([]string, 0)
	excludedPathsLock sync.RWMutex
)

// InitPromethus 初始化Prometheus指标
func InitPromethus() *prometheus.Registry {
	syncOnce.Do(func() {
		if PrometheusInited {
			return
		}
		registry = prometheus.NewRegistry()
		registerer := prometheus.WrapRegistererWith(
			prometheus.Labels{
				"srv_name": ServiceName,
			}, registry,
		)
		// 注册标准收集器
		registerer.MustRegister(
			collectors.NewProcessCollector(collectors.ProcessCollectorOpts{}),
			collectors.NewGoCollector(),
		)

		// 初始化评论相关指标
		commentCreated = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCommentCreated,
			Help: "The total number of created comments",
		}, []string{})
		registerer.MustRegister(commentCreated)

		commentDeleted = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCommentDeleted,
			Help: "The total number of deleted comments",
		}, []string{})
		registerer.MustRegister(commentDeleted)

		// 初始化点赞相关指标
		commentLiked = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCommentLiked,
			Help: "The total number of comment likes",
		}, []string{})
		registerer.MustRegister(commentLiked)

		commentUnliked = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCommentUnliked,
			Help: "The total number of comment unlikes",
		}, []string{})
		registerer.MustRegister(commentUnliked)

		// 初始化缓存相关指标
		cacheHits = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCacheHits,
			Help: "The total number of cache hits",
		}, []string{})
		registerer.MustRegister(cacheHits)

		cacheMisses = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: MetricNameCacheMisses,
			Help: "The total number of cache misses",
		}, []string{})
		registerer.MustRegister(cacheMisses)

		// 初始化请求延迟指标
		requestDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    MetricNameRequestDuration,
				Help:    "The duration of requests in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation"},
		)
		registerer.MustRegister(requestDuration)

		dbOperationDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    MetricNameDBOperationDuration,
				Help:    "The duration of database operations in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation"},
		)
		registerer.MustRegister(dbOperationDuration)

		// 初始化统计同步指标
		statsSyncTotal = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: MetricNameStatsSyncTotal,
				Help: "The total number of stats sync operations",
			},
			[]string{"type"},
		)
		registerer.MustRegister(statsSyncTotal)

		statsSyncErrors = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: MetricNameStatsSyncErrors,
				Help: "The total number of stats sync errors",
			},
			[]string{"type"},
		)
		registerer.MustRegister(statsSyncErrors)

		statsSyncDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    MetricNameStatsSyncDuration,
				Help:    "The duration of stats sync operations",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"type"},
		)
		registerer.MustRegister(statsSyncDuration)

		// 初始化HTTP请求指标
		httpRequestsTotal = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: MetricNameHttpRequestsTotal,
				Help: "Total number of HTTP requests by status code, method, and path",
			},
			[]string{"status", "method", "path"},
		)
		registerer.MustRegister(httpRequestsTotal)

		httpRequestDuration = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    MetricNameHttpRequestDuration,
				Help:    "HTTP request duration in seconds by status code, method, and path",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"status", "method", "path"},
		)
		registerer.MustRegister(httpRequestDuration)

		httpRequestsInFlight = prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: MetricNameHttpRequestsInFlight,
				Help: "Current number of HTTP requests being processed",
			},
		)
		registerer.MustRegister(httpRequestsInFlight)

		// 初始化服务可用性指标
		serviceAvailability = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: MetricNameServiceAvailability,
				Help: "Total number of service requests by status (success/failure)",
			},
			[]string{"status", "error_code", "path", "method"},
		)
		registerer.MustRegister(serviceAvailability)

		errorCodeCounter = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: MetricNameErrorCodes,
				Help: "Total number of service errors by error code",
			},
			[]string{"error_code", "path", "method"},
		)
		registerer.MustRegister(errorCodeCounter)

		// 初始化可用性配置
		initAvailabilityConfig()

		// 标记 Prometheus 已初始化
		PrometheusInited = true
	})

	return registry
}

// 指标操作公共方法

// IncCounter 增加计数器指标值
func IncCounter(counter *prometheus.CounterVec, labelValues ...string) {
	if !PrometheusInited {
		return
	}
	counter.WithLabelValues(labelValues...).Inc()
}

// AddCounter 增加计数器指标值（指定数量）
func AddCounter(counter *prometheus.CounterVec, value float64, labelValues ...string) {
	if !PrometheusInited {
		return
	}
	counter.WithLabelValues(labelValues...).Add(value)
}

// ObserveHistogram 观察直方图指标值
func ObserveHistogram(histogram *prometheus.HistogramVec, value float64, labelValues ...string) {
	if !PrometheusInited {
		return
	}
	histogram.WithLabelValues(labelValues...).Observe(value)
}

// SetGauge 设置仪表盘指标值
func SetGauge(gauge prometheus.Gauge, value float64) {
	if !PrometheusInited {
		return
	}
	gauge.Set(value)
}

// IncGauge 增加仪表盘指标值
func IncGauge(gauge prometheus.Gauge) {
	if !PrometheusInited {
		return
	}
	gauge.Inc()
}

// DecGauge 减少仪表盘指标值
func DecGauge(gauge prometheus.Gauge) {
	if !PrometheusInited {
		return
	}
	gauge.Dec()
}

// 业务相关的指标方法

// RecordCommentCreated 记录评论创建事件
func RecordCommentCreated() {
	IncCounter(commentCreated)
}

// RecordCommentDeleted 记录评论删除事件
func RecordCommentDeleted() {
	IncCounter(commentDeleted)
}

// RecordCommentLiked 记录评论点赞事件
func RecordCommentLiked() {
	IncCounter(commentLiked)
}

// RecordCommentUnliked 记录评论取消点赞事件
func RecordCommentUnliked() {
	IncCounter(commentUnliked)
}

// RecordCacheHit 记录缓存命中事件
func RecordCacheHit() {
	IncCounter(cacheHits)
}

// RecordCacheMiss 记录缓存未命中事件
func RecordCacheMiss() {
	IncCounter(cacheMisses)
}

// RecordRequestDuration 记录请求耗时
func RecordRequestDuration(operation string, duration float64) {
	ObserveHistogram(requestDuration, duration, operation)
}

// RecordDBOperationDuration 记录数据库操作耗时
func RecordDBOperationDuration(operation string, duration float64) {
	ObserveHistogram(dbOperationDuration, duration, operation)
}

// RecordStatsSyncTotal 记录统计同步总数
func RecordStatsSyncTotal(syncType string) {
	IncCounter(statsSyncTotal, syncType)
}

// RecordStatsSyncError 记录统计同步错误
func RecordStatsSyncError(syncType string) {
	IncCounter(statsSyncErrors, syncType)
}

// RecordStatsSyncDuration 记录统计同步耗时
func RecordStatsSyncDuration(syncType string, duration float64) {
	ObserveHistogram(statsSyncDuration, duration, syncType)
}

// RecordHTTPRequest 记录HTTP请求
func RecordHTTPRequest(status, method, path string) {
	IncCounter(httpRequestsTotal, status, method, path)
}

// RecordHTTPRequestDuration 记录HTTP请求耗时
func RecordHTTPRequestDuration(status, method, path string, duration float64) {
	ObserveHistogram(httpRequestDuration, duration, status, method, path)
}

// IncrementHTTPRequestsInFlight 增加正在处理的HTTP请求数
func IncrementHTTPRequestsInFlight() {
	IncGauge(httpRequestsInFlight)
}

// DecrementHTTPRequestsInFlight 减少正在处理的HTTP请求数
func DecrementHTTPRequestsInFlight() {
	DecGauge(httpRequestsInFlight)
}

// initAvailabilityConfig 初始化服务可用性配置
func initAvailabilityConfig() {
	// 监听 Apollo 配置变更
	apollo.AddChangeListener(updateAvailabilityConfig)

	// 初始读取一次配置
	updateAvailabilityConfig()
}

// updateAvailabilityConfig 更新服务可用性配置
func updateAvailabilityConfig() {
	// 更新排除的错误码
	updateExcludedCodes()

	// 更新排除的路径
	updateExcludedPaths()
}

// updateExcludedCodes 更新不计入可用性的错误码配置
func updateExcludedCodes() {
	// 从 Apollo 获取排除的错误码列表，格式如：400,401,404
	codesStr := apollo.GetStringValue("service.availability.excluded_codes", "", "application")
	if codesStr == "" {
		return
	}

	// 解析错误码字符串为整数数组
	var newExcludedCodes []int
	codeStrs := strings.Split(codesStr, ",")
	for _, codeStr := range codeStrs {
		code, err := strconv.Atoi(strings.TrimSpace(codeStr))
		if err != nil {
			continue
		}
		newExcludedCodes = append(newExcludedCodes, code)
	}

	// 如果解析成功，更新排除的错误码
	if len(newExcludedCodes) > 0 {
		excludedCodesLock.Lock()
		excludedCodes = newExcludedCodes
		excludedCodesLock.Unlock()
	}
}

// updateExcludedPaths 更新不计入可用性的路径配置
func updateExcludedPaths() {
	// 从 Apollo 获取排除的路径列表，格式如：/health,/metrics,/internal
	pathsStr := apollo.GetStringValue("service.availability.excluded_paths", "", "application")
	if pathsStr == "" {
		return
	}

	// 解析路径字符串为数组
	var newExcludedPaths []string
	pathStrs := strings.Split(pathsStr, ",")
	for _, pathStr := range pathStrs {
		path := strings.TrimSpace(pathStr)
		if path != "" {
			newExcludedPaths = append(newExcludedPaths, path)
		}
	}

	// 如果解析成功，更新排除的路径
	if len(newExcludedPaths) > 0 {
		excludedPathsLock.Lock()
		excludedPaths = newExcludedPaths
		excludedPathsLock.Unlock()
	}
}

// IsPathExcluded 检查路径是否被排除在可用性统计之外
func IsPathExcluded(path string) bool {
	excludedPathsLock.RLock()
	defer excludedPathsLock.RUnlock()

	for _, excludedPath := range excludedPaths {
		if excludedPath == path || strings.HasPrefix(path, excludedPath+"/") {
			return true
		}
	}

	return false
}

// IsCodeExcluded 检查错误码是否被排除在可用性统计之外
func IsCodeExcluded(code int) bool {
	excludedCodesLock.RLock()
	defer excludedCodesLock.RUnlock()

	for _, excludedCode := range excludedCodes {
		if code == excludedCode {
			return true
		}
	}

	return false
}

// ReportRequestStatus 报告请求状态
func ReportRequestStatus(path, method string, code int, isSuccess bool) {
	if !PrometheusInited {
		return
	}

	// 检查路径是否被排除
	if IsPathExcluded(path) {
		return
	}

	// 记录请求错误码
	if !isSuccess {
		errorCodeCounter.WithLabelValues(strconv.Itoa(code), path, method).Inc()
	}

	// 判断错误码是否应该计入可用性
	excluded := IsCodeExcluded(code)

	// 如果错误码被排除或者请求成功，则统计为可用
	status := "success"
	if !isSuccess && !excluded {
		status = "failure"
	}

	// 更新服务可用性指标
	serviceAvailability.WithLabelValues(
		status,
		strconv.Itoa(code),
		path,
		method,
	).Inc()
}
