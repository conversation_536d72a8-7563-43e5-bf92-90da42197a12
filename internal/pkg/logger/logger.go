package logger

import (
	"context"
	"sync"
	"time"

	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"github.com/XmePlatform/i18n-service/internal/utils"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	Logger *AppLogger
	once   sync.Once
)

// AppLogger 自定义日志结构
type AppLogger struct {
	logger   *zap.Logger
	level    zap.AtomicLevel
	levelStr string
	mu       sync.RWMutex
}

// InitLogger 初始化全局日志
func InitLogger(env string) error {
	var err error
	once.Do(func() {
		Logger, err = newAppLogger(env)
		if err != nil {
			return
		}

		// 添加Apollo配置变更监听
		apollo.AddChangeListener(func() {
			GetConsoleLogger().Info("config files changes begin to update log level")
			level := apollo.GetStringValue("log.level", "info")
			Logger.UpdateLogLevel(level)
		})
	})
	return err
}

// newCustomLogger 创建新的自定义日志实例
func newAppLogger(env string) (*AppLogger, error) {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:       "timestamp",
		LevelKey:      "level",
		NameKey:       "logger",
		CallerKey:     "caller",
		FunctionKey:   zapcore.OmitKey,
		MessageKey:    "message",
		StacktraceKey: "stacktrace",
		LineEnding:    zapcore.DefaultLineEnding,
		EncodeLevel:   zapcore.LowercaseLevelEncoder,
		EncodeTime: func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
			enc.AppendString(t.Format(time.RFC3339Nano))
		},
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 从Apollo获取初始日志级别
	level := apollo.GetStringValue("log.level", "info")
	atomicLevel := zap.NewAtomicLevel()
	if err := atomicLevel.UnmarshalText([]byte(level)); err != nil {
		atomicLevel.SetLevel(zapcore.InfoLevel)
	}

	config := zap.Config{
		Level:            atomicLevel,
		Development:      env != "prod",
		Encoding:         "json",
		EncoderConfig:    encoderConfig,
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
		InitialFields: map[string]interface{}{
			"service": "comment-service",
			"env":     env,
		},
	}

	logger, err := config.Build(
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
	)
	if err != nil {
		return nil, err
	}

	return &AppLogger{
		logger:   logger,
		level:    atomicLevel,
		levelStr: level,
	}, nil
}

// UpdateLogLevel 更新日志级别
func (l *AppLogger) UpdateLogLevel(level string) {
	if l.levelStr == level {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	if err := l.level.UnmarshalText([]byte(level)); err != nil {
		l.logger.Warn("Failed to update log level", zap.String("level", level), zap.Error(err))
	} else {
		l.levelStr = level
		l.logger.Info("Log level updated", zap.String("level", level))
	}
}

// contextFields 从上下文中提取字段
func (l *AppLogger) contextFields(ctx context.Context) []zap.Field {
	fields := make([]zap.Field, 0, 3)

	// 添加请求ID
	if requestID := ctx.Value(utils.ContextKeyRequestID); requestID != nil {
		if id, ok := requestID.(string); ok {
			fields = append(fields, zap.String("request_id", id))
		}
	}

	// 添加用户ID
	if userID := utils.GetUserID(ctx); userID > 0 {
		fields = append(fields, zap.Uint64("user_id", userID))
	}

	// 添加时间戳
	fields = append(fields, zap.Time("timestamp", time.Now()))

	return fields
}

// WithContext 创建带有上下文的日志记录器
func (l *AppLogger) WithContext(ctx context.Context) *zap.Logger {
	return l.logger.With(l.contextFields(ctx)...)
}

// Debug 输出Debug级别日志
func (l *AppLogger) Debug(ctx context.Context, msg string, fields ...zap.Field) {
	l.logger.Debug(msg, append(l.contextFields(ctx), fields...)...)
}

// Info 输出Info级别日志
func (l *AppLogger) Info(ctx context.Context, msg string, fields ...zap.Field) {
	l.logger.Info(msg, append(l.contextFields(ctx), fields...)...)
}

// Warn 输出Warn级别日志
func (l *AppLogger) Warn(ctx context.Context, msg string, fields ...zap.Field) {
	l.logger.Warn(msg, append(l.contextFields(ctx), fields...)...)
}

// Error 输出Error级别日志
func (l *AppLogger) Error(ctx context.Context, msg string, fields ...zap.Field) {
	l.logger.Error(msg, append(l.contextFields(ctx), fields...)...)
}

// Fatal 输出Fatal级别日志
func (l *AppLogger) Fatal(ctx context.Context, msg string, fields ...zap.Field) {
	l.logger.Fatal(msg, append(l.contextFields(ctx), fields...)...)
}

// GetLogger 获取全局日志实例
func GetLogger() *AppLogger {
	if Logger == nil {
		// 如果未初始化，使用默认配置
		appLogger, _ := newAppLogger("prod")
		Logger = appLogger
		return appLogger
	}
	return Logger
}

// GetZapLogger 获取zap日志实例
func GetZapLogger() *zap.Logger {
	if Logger == nil {
		// 如果未初始化，使用默认配置
		appLogger, _ := newAppLogger("prod")
		Logger = appLogger
		return appLogger.logger
	}
	return Logger.logger
}

// Sync 同步日志
func Sync() error {
	if Logger != nil {
		return Logger.logger.Sync()
	}
	return nil
}
