package logger

import (
	"strings"
	"sync"

	"time"

	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"go.uber.org/zap"
	"gorm.io/gorm/logger"
)

var (
	MySQLLogger     *logger.Interface
	mysqlLogLevel   logger.LogLevel
	mysqlLogLevelMu sync.RWMutex
)

// 初始化 MySQL 日志级别
func InitMysqlLogger() {
	// 设置默认日志级别
	updateMySQLLogLevel()

	// 监听配置变更
	apollo.AddChangeListener(updateMySQLLogLevel)
}

// 更新 MySQL 日志级别
func updateMySQLLogLevel() {
	// 从 Apollo 读取日志级别配置
	logLevelStr := strings.ToLower(apollo.GetStringValue("mysql.log.level", "error", "application"))

	var level logger.LogLevel
	switch logLevelStr {
	case "silent":
		level = logger.Silent
	case "error":
		level = logger.Error
	case "warn":
		level = logger.Warn
	case "info":
		level = logger.Info
	default:
		level = logger.Info
	}

	mysqlLogLevelMu.Lock()
	mysqlLogLevel = level
	mysqlLogLevelMu.Unlock()

	// 如果已经初始化了 logger，需要重新创建
	if MySQLLogger != nil {
		mysqlLogger := GetMysqlLogger(GetZapLogger())
		MySQLLogger = &mysqlLogger
	}
}

// GetMysqlLogger 获取 MySQL 日志配置
func GetMysqlLogger(z *zap.Logger) logger.Interface {
	// 从缓存获取已创建的日志记录器
	if MySQLLogger != nil {
		return *MySQLLogger
	}

	// 获取当前日志级别
	mysqlLogLevelMu.RLock()
	level := mysqlLogLevel
	mysqlLogLevelMu.RUnlock()

	// 从 Apollo 获取慢查询阈值 (毫秒)
	slowThreshold := time.Duration(apollo.GetIntValue("mysql.slow_threshold", 200)) * time.Millisecond

	// 是否忽略记录未找到错误
	ignoreRecordNotFound := apollo.GetBoolValue("mysql.ignore_record_not_found", true)

	mysqlLogger := logger.New(
		zap.NewStdLog(z), // 将 zap 包装为 io.Writer
		logger.Config{
			SlowThreshold:             slowThreshold,        // 慢查询阈值
			LogLevel:                  level,                // 动态日志级别
			IgnoreRecordNotFoundError: ignoreRecordNotFound, // 是否忽略记录未找到错误
			Colorful:                  false,                // 禁用颜色（JSON 格式不需要）
		},
	)

	// 保存到全局变量
	MySQLLogger = &mysqlLogger
	return mysqlLogger
}
