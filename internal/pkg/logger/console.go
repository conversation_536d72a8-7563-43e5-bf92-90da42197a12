package logger

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	consoleLogger *ConsoleLogger
	consoleOnce   sync.Once
)

// ConsoleLogger 控制台日志记录器
type ConsoleLogger struct {
	logger *zap.Logger
	level  zap.AtomicLevel
	mu     sync.RWMutex
}

// NewConsoleLogger 创建新的控制台日志记录器
func NewConsoleLogger(level string) (*ConsoleLogger, error) {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		Caller<PERSON>ey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalColorLevelEncoder, // 使用彩色输出
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	atomicLevel := zap.NewAtomicLevel()
	if err := atomicLevel.UnmarshalText([]byte(level)); err != nil {
		atomicLevel.SetLevel(zapcore.InfoLevel)
	}

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		atomicLevel,
	)

	logger := zap.New(
		core,
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
	)

	return &ConsoleLogger{
		logger: logger,
		level:  atomicLevel,
	}, nil
}

// GetConsoleLogger 获取全局控制台日志记录器实例
func GetConsoleLogger() *ConsoleLogger {
	consoleOnce.Do(func() {
		var err error
		consoleLogger, err = NewConsoleLogger("info")
		if err != nil {
			fmt.Printf("Failed to create console logger: %v\n", err)
			os.Exit(1)
		}
	})
	return consoleLogger
}

// contextFields 从上下文中提取字段
func (l *ConsoleLogger) contextFields(ctx context.Context) []zap.Field {
	fields := make([]zap.Field, 0, 3)
	fields = append(fields, zap.Time("timestamp", time.Now()))
	return fields
}

// Debug 输出 Debug 级别日志
func (l *ConsoleLogger) Debug(msg string, fields ...zap.Field) {
	l.logger.Debug(msg, fields...)
}

// Info 输出 Info 级别日志
func (l *ConsoleLogger) Info(msg string, fields ...zap.Field) {
	l.logger.Info(msg, fields...)
}

// Warn 输出 Warn 级别日志
func (l *ConsoleLogger) Warn(msg string, fields ...zap.Field) {
	l.logger.Warn(msg, fields...)
}

// Error 输出 Error 级别日志
func (l *ConsoleLogger) Error(msg string, fields ...zap.Field) {
	l.logger.Error(msg, fields...)
}

// Fatal 输出 Fatal 级别日志
func (l *ConsoleLogger) Fatal(msg string, fields ...zap.Field) {
	l.logger.Fatal(msg, fields...)
}

// WithContext 创建带有上下文的日志记录器
func (l *ConsoleLogger) WithContext(ctx context.Context) *zap.Logger {
	return l.logger.With(l.contextFields(ctx)...)
}

// Sync 同步日志
func (l *ConsoleLogger) Sync() error {
	return l.logger.Sync()
}
