package handler

import (
	"context"
	"fmt"
	"github.com/XmePlatform/i18n-service/internal/model/response"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/XmePlatform/i18n-service/internal/service"
	"go.uber.org/zap"

	"github.com/gofiber/fiber/v2"
)

type I18nHandler struct {
	service *service.I18nService
}

func NewI18nHandler(service *service.I18nService) *I18nHandler {
	return &I18nHandler{service: service}
}

// GetTranslations 获取翻译内容
func (h *I18nHandler) GetTranslations(c *fiber.Ctx) error {
	namespace := c.Params("namespace")

	// 获取查询参数
	version := c.Query("version")
	lang := c.Query("lang")

	if namespace == "" {
		return response.InvalidParam(c)
	}

	translations, err := h.service.GetTranslations(namespace, lang, version)
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to get translations", zap.Error(err))
		return response.Error(c, 500, err)
	}

	return response.CustomResponse(c, 200, "success", true, translations)
}

// GetTranslationKey 获取单个翻译键
func (h *I18nHandler) GetTranslationKey(c *fiber.Ctx) error {
	namespace := c.Params("namespace")
	key := c.Params("key")

	lang := c.Query("lang")
	if lang == "" {
		lang = "en_US" // 默认语言
	}

	if namespace == "" || key == "" {
		return response.InvalidParam(c)
	}

	value, err := h.service.GetTranslationKey(namespace, key, lang)
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to get translation key", zap.Error(err))
		// 即使出错也返回key本身，保证业务不中断
		value = key
	}

	result := map[string]interface{}{
		"namespace": namespace,
		"key":       key,
		"language":  lang,
		"value":     value,
	}

	return response.CustomResponse(c, 200, "success", true, result)
}



// GetNamespaces 获取所有命名空间
func (h *I18nHandler) GetNamespaces(c *fiber.Ctx) error {
	namespaces, err := h.service.GetNamespaces()
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to get namespaces", zap.Error(err))
		return response.Error(c, 500, err)
	}

	return response.CustomResponse(c, 200, "success", true, namespaces)
}

// GetReleases 获取版本列表
func (h *I18nHandler) GetReleases(c *fiber.Ctx) error {
	namespace := c.Params("namespace")

	if namespace == "" {
		return response.InvalidParam(c)
	}

	releases, err := h.service.GetReleases(namespace)
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to get releases", zap.Error(err))
		return response.Error(c, 500, err)
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    releases,
	})
}

// 注意：InvalidateAllCache 操作已移除，因为清空所有缓存过于危险
// 请使用更精确的缓存失效方法：
// - InvalidateNamespaceVersionCache: 清空指定命名空间和版本的缓存
// - InvalidateTranslationCache: 清空指定命名空间的翻译缓存  
// - InvalidateVersionCache: 清空指定命名空间的版本缓存

// InvalidateNamespaceVersionCache 清空指定命名空间和版本的缓存
func (h *I18nHandler) InvalidateNamespaceVersionCache(c *fiber.Ctx) error {
	namespace := c.Params("namespace")
	if namespace == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"message": "Namespace is required",
		})
	}

	version := c.Query("version", "") // 可选的版本参数

	err := h.service.InvalidateNamespaceVersionCache(namespace, version)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"message": "Failed to invalidate namespace version cache",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Namespace version cache invalidated successfully",
	})
}

// InvalidateReleaseListCache 清空版本列表缓存（用于版本发布等状态变更）
func (h *I18nHandler) InvalidateReleaseListCache(c *fiber.Ctx) error {
	namespace := c.Params("namespace")
	if namespace == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"message": "Namespace is required",
		})
	}

	err := h.service.InvalidateReleaseListCache(namespace)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"message": "Failed to invalidate release list cache",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Release list cache invalidated successfully",
	})
}

// InvalidateTranslationCache 清空指定命名空间的翻译缓存
func (h *I18nHandler) InvalidateTranslationCache(c *fiber.Ctx) error {
	namespace := c.Params("namespace")
	if namespace == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"message": "Namespace is required",
		})
	}

	err := h.service.InvalidateTranslationCache(namespace)
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to invalidate translation cache", 
			zap.String("namespace", namespace), 
			zap.Error(err))
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"message": "Failed to invalidate translation cache",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": fmt.Sprintf("Translation cache for namespace '%s' invalidated successfully", namespace),
	})
}

// InvalidateVersionCache 清空版本相关缓存
func (h *I18nHandler) InvalidateVersionCache(c *fiber.Ctx) error {
	namespace := c.Params("namespace")
	if namespace == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"message": "Namespace is required",
		})
	}

	err := h.service.InvalidateVersionCache(namespace)
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to invalidate version cache", 
			zap.String("namespace", namespace), 
			zap.Error(err))
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"message": "Failed to invalidate version cache",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": fmt.Sprintf("Version cache for namespace '%s' invalidated successfully", namespace),
	})
}

// GetHealth 健康检查
func (h *I18nHandler) GetHealth(c *fiber.Ctx) error {
	health := h.service.GetHealth()
	return response.CustomResponse(c, 200, "success", true, health)
}


