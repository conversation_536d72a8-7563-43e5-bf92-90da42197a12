package database

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"time"

	"github.com/XmePlatform/i18n-service/internal/config"

	_ "github.com/go-sql-driver/mysql"
)

type DB struct {
	*gorm.DB
	sqlDB *sql.DB
}

func Connect(cfg *config.Config) (*DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.DB.User, cfg.DB.Password, cfg.DB.Host, cfg.DB.Port, cfg.DB.DBName)

	gormDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.GetMysqlLogger(logger.GetZapLogger()).LogMode(gormlogger.Info),
	})
	if err != nil {
		logger.GetConsoleLogger().Fatal("Failed to connect to database", zap.Error(err))
		return nil, err
	}
	
	sqlDB, err := gormDB.DB()
	if err != nil {
		panic("Failed to connect to database")
	}
	
	sqlDB.SetMaxIdleConns(apollo.GetIntValue("db.max_idle_conns", 10))                                     // 设置最大空闲连接数
	sqlDB.SetMaxOpenConns(apollo.GetIntValue("db.max_open_conns", 50))                                     // 设置最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Duration(apollo.GetIntValue("db.conn_max_life_time", 1)) * time.Hour)    // 设置连接最大生命周期
	sqlDB.SetConnMaxIdleTime(time.Duration(apollo.GetIntValue("db.conn_max_idle_time", 30)) * time.Minute) // 设置连接空闲最大时间
	
	return &DB{DB: gormDB, sqlDB: sqlDB}, nil
}

func (db *DB) Close() error {
	logger.GetLogger().Info(context.Background(), "Closing database connection")
	return db.sqlDB.Close()
}

func (db *DB) Health() error {
	return db.sqlDB.Ping()
}
