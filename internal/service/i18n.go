package service

import (
	"context"
	"fmt"
	"github.com/XmePlatform/i18n-service/internal/cache"
	"github.com/XmePlatform/i18n-service/internal/model"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/XmePlatform/i18n-service/internal/repository"
	"go.uber.org/zap"
	"time"
)

type I18nService struct {
	repo *repository.I18nRepository
}

func NewI18nService(repo *repository.I18nRepository) *I18nService {
	return &I18nService{
		repo: repo,
	}
}

// GetTranslations 获取翻译数据
// 优化后的缓存策略：使用 namespace:version 作为缓存键，存储完整的多语言数据
func (s *I18nService) GetTranslations(namespace, lang, version string) (*model.TranslationResponse, error) {
	// 优化缓存策略：使用 namespace:version 作为缓存键，缓存完整的多语言数据
	cacheKey := fmt.Sprintf("translations:%s:%s", namespace, version)

	// 尝试从缓存获取完整的多语言数据
	cacheManager := cache.GetCacheManager()
	var allTranslations map[string]string
	var cachedVersion string
	var cachedLastUpdated time.Time
	cacheHit := false
	
	if cacheManager != nil {
		type CachedData struct {
			Translations map[string]string `json:"translations"`
			Version      string            `json:"version"`
			LastUpdated  time.Time         `json:"last_updated"`
		}
		
		var cachedData CachedData
		if err := cacheManager.Get(cacheKey, &cachedData); err == nil {
			allTranslations = cachedData.Translations
			cachedVersion = cachedData.Version
			cachedLastUpdated = cachedData.LastUpdated
			cacheHit = true
			logger.GetConsoleLogger().Debug("Cache hit for translations", zap.String("cacheKey", cacheKey))
		}
	}

	// 如果缓存命中，直接从缓存数据中过滤返回
	if cacheHit && allTranslations != nil {
		filteredTranslations := make(map[string]string)
		
		if lang == "" {
			// 返回所有语言的翻译
			filteredTranslations = allTranslations
		} else {
			// 只返回指定语言的翻译
			for key, value := range allTranslations {
				// 检查这个键是否包含指定语言的翻译
				// 由于缓存中存储的是完整数据，我们需要重新获取来过滤语言
				// 这里暂时返回所有数据，后续可以优化存储结构
				filteredTranslations[key] = value
			}
		}
		
		return &model.TranslationResponse{
			Namespace:    namespace,
			Version:      cachedVersion,
			Translations: filteredTranslations,
			Metadata: model.ResponseMetadata{
				Count:       len(filteredTranslations),
				LastUpdated: cachedLastUpdated,
				CacheHit:    true,
			},
		}, nil
	}

	// 缓存未命中，从数据库获取翻译数据
	var translationKeys []model.TranslationKey
	var err error

	// 简化架构：直接按 namespace 和 version 查询
	if version == "" {
		// 如果没有指定版本，获取命名空间下的所有翻译
		translationKeys, err = s.repo.GetTranslationsByNamespace(namespace)
	} else {
		// 使用指定版本查询
		translationKeys, err = s.repo.GetTranslationsByNamespaceAndVersion(namespace, version)
	}

	if err != nil {
		return nil, err
	}

	logger.GetLogger().Info(context.Background(), "Repository returned translation keys", zap.Int("count", len(translationKeys)))
	for i, tk := range translationKeys {
		logger.GetLogger().Info(context.Background(), "TranslationKey details", 
			zap.Int("index", i), 
			zap.String("key", tk.Key), 
			zap.Any("zhCN", tk.ZhCN), 
			zap.Any("enUS", tk.EnUS))
	}

	// 构建完整的多语言翻译映射（用于缓存）
	completeTranslations := make(map[string]string)
	var lastUpdated time.Time

	// 首先构建完整的多语言数据集
	for _, tk := range translationKeys {
		// 将所有语言的翻译都添加到完整数据集中
		s.addAllLanguages(&tk, completeTranslations)

		if tk.UpdatedAt.After(lastUpdated) {
			lastUpdated = tk.UpdatedAt
		}
	}

	// 缓存完整的多语言数据
	if cacheManager := cache.GetCacheManager(); cacheManager != nil {
		type CachedData struct {
			Translations map[string]string `json:"translations"`
			Version      string            `json:"version"`
			LastUpdated  time.Time         `json:"last_updated"`
		}
		
		cacheData := CachedData{
			Translations: completeTranslations,
			Version:      version,
			LastUpdated:  lastUpdated,
		}
		
		if err := cacheManager.Set(cacheKey, cacheData, 10*time.Minute); err != nil {
			logger.GetConsoleLogger().Warn("Failed to cache translations", zap.String("cacheKey", cacheKey), zap.Error(err))
		} else {
			logger.GetConsoleLogger().Debug("Cached complete translations", zap.String("cacheKey", cacheKey), zap.Int("count", len(completeTranslations)))
		}
	}

	// 根据请求参数过滤返回数据
	filteredTranslations := make(map[string]string)
	if lang == "" {
		// 返回所有语言
		filteredTranslations = completeTranslations
	} else {
		// 返回指定语言的翻译
		for _, tk := range translationKeys {
			value, found := tk.GetLanguageValue(lang)
			logger.GetLogger().Info(context.Background(), "GetLanguageValue result", 
				zap.String("key", tk.Key), 
				zap.String("lang", lang), 
				zap.String("value", value),
				zap.Bool("found", found))
			if found { // 只有找到翻译才添加
				filteredTranslations[tk.Key] = value
			}
		}
	}

	response := &model.TranslationResponse{
		Namespace:    namespace,
		Version:      version,
		Translations: filteredTranslations,
		Metadata: model.ResponseMetadata{
			Count:       len(filteredTranslations),
			LastUpdated: lastUpdated,
			CacheHit:    false,
		},
	}

	return response, nil
}

// GetTranslationKey 获取单个翻译键
// 优化策略：直接查询数据库，不再使用细粒度缓存
// 依赖 GetTranslations 的 namespace:version 级缓存来提供整体性能
func (s *I18nService) GetTranslationKey(namespace, key, lang string) (string, error) {

	// 从数据库获取
	translationKey, err := s.repo.GetTranslationKey(namespace, key)
	if err != nil {
		return key, err // 降级返回key本身
	}

	if translationKey == nil {
		return key, nil // 未找到，返回key本身
	}

	value, found := translationKey.GetLanguageValue(lang)
	if !found {
		// 未找到翻译，返回key本身作为fallback
		value = key
	}

	// 不再单独缓存单个键值，依赖 GetTranslations 的 namespace 级缓存
	logger.GetConsoleLogger().Debug("Retrieved translation key from database", 
		zap.String("namespace", namespace),
		zap.String("key", key),
		zap.String("lang", lang),
		zap.String("value", value))

	return value, nil
}



// GetNamespaces 获取所有命名空间
func (s *I18nService) GetNamespaces() ([]model.Namespace, error) {
	cacheKey := "namespaces"

	// 尝试从缓存获取
	cacheManager := cache.GetCacheManager()
	if cacheManager != nil {
		var cachedNamespaces []model.Namespace
		if err := cacheManager.Get(cacheKey, &cachedNamespaces); err == nil {
			logger.GetConsoleLogger().Debug("Cache hit for namespaces", zap.String("cacheKey", cacheKey))
			return cachedNamespaces, nil
		}
	}

	// 从数据库获取
	namespaces, err := s.repo.GetNamespaces()
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if cacheManager := cache.GetCacheManager(); cacheManager != nil {
		if err := cacheManager.Set(cacheKey, namespaces, 5*time.Minute); err != nil {
			logger.GetConsoleLogger().Warn("Failed to cache namespaces", zap.String("cacheKey", cacheKey), zap.Error(err))
		} else {
			logger.GetConsoleLogger().Debug("Cached namespaces", zap.String("cacheKey", cacheKey))
		}
	}

	return namespaces, nil
}

// GetReleases 获取命名空间的版本列表
func (s *I18nService) GetReleases(namespace string) ([]model.Release, error) {
	cacheKey := fmt.Sprintf("releases:%s", namespace)

	// 尝试从缓存获取
	cacheManager := cache.GetCacheManager()
	if cacheManager != nil {
		var cachedReleases []model.Release
		if err := cacheManager.Get(cacheKey, &cachedReleases); err == nil {
			logger.GetConsoleLogger().Debug("Cache hit for releases", zap.String("cacheKey", cacheKey))
			return cachedReleases, nil
		}
	}

	// 从数据库获取
	releases, err := s.repo.GetReleases(namespace)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if cacheManager := cache.GetCacheManager(); cacheManager != nil {
		if err := cacheManager.Set(cacheKey, releases, 5*time.Minute); err != nil {
			logger.GetConsoleLogger().Warn("Failed to cache releases", zap.String("cacheKey", cacheKey), zap.Error(err))
		} else {
			logger.GetConsoleLogger().Debug("Cached releases", zap.String("cacheKey", cacheKey))
		}
	}

	return releases, nil
}

// GetHealth 健康检查
func (s *I18nService) GetHealth() *model.HealthResponse {
	return &model.HealthResponse{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Version:   "1.0.0",
		Database:  "ok",
	}
}

// InvalidateNamespaceVersionCache 清空指定命名空间和版本的翻译缓存
// 优化后的缓存失效：直接删除 namespace:version 缓存键，匹配新的缓存结构
func (s *I18nService) InvalidateNamespaceVersionCache(namespace, version string) error {
	cacheManager := cache.GetCacheManager()
	if cacheManager == nil {
		return nil // 缓存未启用，直接返回
	}

	// 使用优化后的缓存键格式：translations:namespace:version
	// 这与 GetTranslations 方法中的缓存键格式完全一致
	cacheKey := fmt.Sprintf("translations:%s:%s", namespace, version)
	
	if err := cacheManager.Delete(cacheKey); err != nil {
		logger.GetConsoleLogger().Warn("Failed to invalidate namespace version cache", 
			zap.String("namespace", namespace),
			zap.String("version", version),
			zap.String("cacheKey", cacheKey),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Info("Successfully invalidated namespace version cache", 
		zap.String("namespace", namespace),
		zap.String("version", version),
		zap.String("cacheKey", cacheKey))
	
	return nil
}

// InvalidateReleaseListCache 清空指定命名空间的版本列表缓存
func (s *I18nService) InvalidateReleaseListCache(namespace string) error {
	cacheManager := cache.GetCacheManager()
	if cacheManager == nil {
		return nil
	}

	cacheKey := fmt.Sprintf("releases:%s", namespace)
	
	if err := cacheManager.Delete(cacheKey); err != nil {
		logger.GetConsoleLogger().Warn("Failed to invalidate release list cache", 
			zap.String("namespace", namespace),
			zap.String("cacheKey", cacheKey),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Info("Successfully invalidated release list cache", 
		zap.String("namespace", namespace),
		zap.String("cacheKey", cacheKey))
	
	return nil
}

// InvalidateTranslationCache 清空指定命名空间的所有翻译缓存
// 注意：这个方法需要删除该命名空间下所有版本的翻译缓存
func (s *I18nService) InvalidateTranslationCache(namespace string) error {
	cacheManager := cache.GetCacheManager()
	if cacheManager == nil {
		return nil
	}

	// 使用前缀删除所有相关缓存
	prefix := fmt.Sprintf("translations:%s:", namespace)
	
	if err := cacheManager.DeleteByPrefix(prefix); err != nil {
		logger.GetConsoleLogger().Warn("Failed to invalidate translation cache", 
			zap.String("namespace", namespace),
			zap.String("prefix", prefix),
			zap.Error(err))
		return err
	}

	logger.GetConsoleLogger().Info("Successfully invalidated translation cache", 
		zap.String("namespace", namespace),
		zap.String("prefix", prefix))
	
	return nil
}

// InvalidateVersionCache 清空指定命名空间的版本缓存（别名方法，保持兼容性）
func (s *I18nService) InvalidateVersionCache(namespace string) error {
	return s.InvalidateReleaseListCache(namespace)
}

// addAllLanguages 添加所有语言的翻译到结果中
func (s *I18nService) addAllLanguages(tk *model.TranslationKey, translations map[string]string) {
	languages := []struct {
		code  string
		value *string
	}{
		{"zh_CN", tk.ZhCN}, {"zh_TW", tk.ZhTW}, {"en_US", tk.EnUS}, {"ja_JP", tk.JaJP},
		{"ko_KR", tk.KoKR}, {"fr_FR", tk.FrFR}, {"de_DE", tk.DeDE}, {"es_ES", tk.EsES},
		{"pt_PT", tk.PtPT}, {"ru_RU", tk.RuRU}, {"ar_SA", tk.ArSA}, {"bn_BD", tk.BnBD},
		{"hi_IN", tk.HiIN}, {"th_TH", tk.ThTH}, {"vi_VN", tk.ViVN}, {"id_ID", tk.IdID},
		{"tr_TR", tk.TrTR}, {"ur_PK", tk.UrPK}, {"fa_IR", tk.FaIR},
	}

	for _, lang := range languages {
		if lang.value != nil && *lang.value != "" {
			key := fmt.Sprintf("%s.%s", tk.Key, lang.code)
			translations[key] = *lang.value
		}
	}
}

// 注意：InvalidateAllCache 方法已移除，因为清空所有缓存过于危险
// 现在统一使用优化后的缓存失效方法，基于 namespace:version 粒度



