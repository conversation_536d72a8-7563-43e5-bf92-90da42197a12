package model

import "time"

// User represents a user in the system
type User struct {
	ID       string `json:"id" gorm:"column:id"`
	Email    string `json:"email" gorm:"column:email"`
	Username string `json:"username" gorm:"column:username"`
}

func (t *User) TableName() string {
	return "users"
}

// Namespace represents a functional module grouping
type Namespace struct {
	ID          string    `json:"id" gorm:"column:id"`
	Name        string    `json:"name" gorm:"column:name"`
	DisplayName string    `json:"displayName" gorm:"column:displayName"`
	Description *string   `json:"description,omitempty" gorm:"column:description"`
	CreatedBy   string    `json:"createdBy" gorm:"column:createdBy"`
	CreatedAt   time.Time `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"column:updatedAt"`
}

func (t *Namespace) TableName() string {
	return "namespaces"
}

// TranslationKey represents a translation key with language values
type TranslationKey struct {
	ID          string    `json:"id" gorm:"column:id"`
	Key         string    `json:"key" gorm:"column:key"`
	NamespaceID string    `json:"namespaceId" gorm:"column:namespaceId"`
	Description *string   `json:"description,omitempty" gorm:"column:description"`
	CreatedBy   string    `json:"createdBy" gorm:"column:createdBy"`
	ReleaseID   *string   `json:"releaseId,omitempty" gorm:"column:releaseId"`
	CreatedAt   time.Time `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"column:updatedAt"`

	// 19 language fields
	ZhCN *string `json:"zh_CN,omitempty" gorm:"column:zh_CN"`
	ZhTW *string `json:"zh_TW,omitempty" gorm:"column:zh_TW"`
	EnUS *string `json:"en_US,omitempty" gorm:"column:en_US"`
	JaJP *string `json:"ja_JP,omitempty" gorm:"column:ja_JP"`
	KoKR *string `json:"ko_KR,omitempty" gorm:"column:ko_KR"`
	FrFR *string `json:"fr_FR,omitempty" gorm:"column:fr_FR"`
	DeDE *string `json:"de_DE,omitempty" gorm:"column:de_DE"`
	EsES *string `json:"es_ES,omitempty" gorm:"column:es_ES"`
	PtPT *string `json:"pt_PT,omitempty" gorm:"column:pt_PT"`
	RuRU *string `json:"ru_RU,omitempty" gorm:"column:ru_RU"`
	ArSA *string `json:"ar_SA,omitempty" gorm:"column:ar_SA"`
	BnBD *string `json:"bn_BD,omitempty" gorm:"column:bn_BD"`
	HiIN *string `json:"hi_IN,omitempty" gorm:"column:hi_IN"`
	ThTH *string `json:"th_TH,omitempty" gorm:"column:th_TH"`
	ViVN *string `json:"vi_VN,omitempty" gorm:"column:vi_VN"`
	IdID *string `json:"id_ID,omitempty" gorm:"column:id_ID"`
	TrTR *string `json:"tr_TR,omitempty" gorm:"column:tr_TR"`
	UrPK *string `json:"ur_PK,omitempty" gorm:"column:ur_PK"`
	FaIR *string `json:"fa_IR,omitempty" gorm:"column:fa_IR"`
}

func (t *TranslationKey) TableName() string {
	return "translation_keys"
}

// Release represents a version release (always tied to a namespace)
type Release struct {
	ID          string    `json:"id" gorm:"column:id"`
	Version     string    `json:"version" gorm:"column:version"`
	Description *string   `json:"description,omitempty" gorm:"column:description"`
	Status      string    `json:"status" gorm:"column:status"` // DRAFT, PUBLISHED, ARCHIVED
	NamespaceID string    `json:"namespaceId" gorm:"column:namespaceId"` // Required - no global releases
	CreatedAt   time.Time `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"column:updatedAt"`
}

func (t *Release) TableName() string {
	return "releases"
}

// API Response models
type TranslationResponse struct {
	Namespace    string            `json:"namespace"`
	Environment  string            `json:"environment,omitempty"`
	Version      string            `json:"version,omitempty"`
	Translations map[string]string `json:"translations"`
	Metadata     ResponseMetadata  `json:"metadata"`
}

type ResponseMetadata struct {
	Count       int       `json:"count"`
	LastUpdated time.Time `json:"lastUpdated"`
	CacheHit    bool      `json:"cacheHit,omitempty"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
	Database  string `json:"database"`
}

// GetLanguageValue returns the value for a specific language from TranslationKey
// Returns (value, found) where found indicates if the translation was found
func (tk *TranslationKey) GetLanguageValue(lang string) (string, bool) {
	switch lang {
	case "zh_CN", "zh-CN":
		if tk.ZhCN != nil && *tk.ZhCN != "" {
			return *tk.ZhCN, true
		}
	case "zh_TW", "zh-TW":
		if tk.ZhTW != nil && *tk.ZhTW != "" {
			return *tk.ZhTW, true
		}
	case "en_US", "en-US", "en":
		if tk.EnUS != nil && *tk.EnUS != "" {
			return *tk.EnUS, true
		}
	case "ja_JP", "ja-JP", "ja":
		if tk.JaJP != nil && *tk.JaJP != "" {
			return *tk.JaJP, true
		}
	case "ko_KR", "ko-KR", "ko":
		if tk.KoKR != nil && *tk.KoKR != "" {
			return *tk.KoKR, true
		}
	case "fr_FR", "fr-FR", "fr":
		if tk.FrFR != nil && *tk.FrFR != "" {
			return *tk.FrFR, true
		}
	case "de_DE", "de-DE", "de":
		if tk.DeDE != nil && *tk.DeDE != "" {
			return *tk.DeDE, true
		}
	case "es_ES", "es-ES", "es":
		if tk.EsES != nil && *tk.EsES != "" {
			return *tk.EsES, true
		}
	case "pt_PT", "pt-PT", "pt":
		if tk.PtPT != nil && *tk.PtPT != "" {
			return *tk.PtPT, true
		}
	case "ru_RU", "ru-RU", "ru":
		if tk.RuRU != nil && *tk.RuRU != "" {
			return *tk.RuRU, true
		}
	case "ar_SA", "ar-SA", "ar":
		if tk.ArSA != nil && *tk.ArSA != "" {
			return *tk.ArSA, true
		}
	case "bn_BD", "bn-BD", "bn":
		if tk.BnBD != nil && *tk.BnBD != "" {
			return *tk.BnBD, true
		}
	case "hi_IN", "hi-IN", "hi":
		if tk.HiIN != nil && *tk.HiIN != "" {
			return *tk.HiIN, true
		}
	case "th_TH", "th-TH", "th":
		if tk.ThTH != nil && *tk.ThTH != "" {
			return *tk.ThTH, true
		}
	case "vi_VN", "vi-VN", "vi":
		if tk.ViVN != nil && *tk.ViVN != "" {
			return *tk.ViVN, true
		}
	case "id_ID", "id-ID", "id":
		if tk.IdID != nil && *tk.IdID != "" {
			return *tk.IdID, true
		}
	case "tr_TR", "tr-TR", "tr":
		if tk.TrTR != nil && *tk.TrTR != "" {
			return *tk.TrTR, true
		}
	case "ur_PK", "ur-PK", "ur":
		if tk.UrPK != nil && *tk.UrPK != "" {
			return *tk.UrPK, true
		}
	case "fa_IR", "fa-IR", "fa":
		if tk.FaIR != nil && *tk.FaIR != "" {
			return *tk.FaIR, true
		}
	}
	return "", false // 未找到翻译
}
