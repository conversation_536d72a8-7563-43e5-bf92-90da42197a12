package response

import (
	"github.com/gofiber/fiber/v2"
)

// Response 通用响应结构
type Response[T any] struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Success   bool   `json:"success"`
	Result    T      `json:"result,omitempty"`
	RequestID string `json:"request_id,omitempty"`
	EpValue   int    `json:"ep_value,omitempty"`
}

// PageResult 分页数据结构
type PageResult[T any] struct {
	List     []T   `json:"list"`
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

// NewResponse 创建新的响应
func NewResponse[T any](c *fiber.Ctx, code int, message string, success bool, result T) Response[T] {
	return Response[T]{
		Code:      code,
		Message:   message,
		Success:   success,
		Result:    result,
		RequestID: GetRequestID(c),
	}
}

// GetRequestID 获取请求ID
func GetRequestID(c *fiber.Ctx) string {
	return c.Locals("request_id").(string)
}

// Error 返回错误响应
func Error(c *fiber.Ctx, code int, err error) error {
	// 记录错误码用于可用性统计
	c.Locals("response_code", code)
	c.Locals("response_success", false)

	return c.Status(fiber.StatusOK).JSON(NewResponse[any](c, code, err.Error(), false, nil))
}

// Unauthorized 返回未授权响应
func Unauthorized(c *fiber.Ctx) error {

	return c.Status(fiber.StatusUnauthorized).JSON(NewResponse[any](
		c,
		fiber.StatusUnauthorized,
		"",
		false,
		nil,
	))
}

// InvalidParam 返回参数错误响应
func InvalidParam(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(NewResponse[any](
		c,
		fiber.StatusBadRequest,
		"",
		false,
		nil,
	))
}

// CustomResponse 返回自定义响应
func CustomResponse[T any](c *fiber.Ctx, code int, message string, success bool, result T) error {

	// 记录响应码用于可用性统计
	c.Locals("response_code", code)
	c.Locals("response_success", success)

	return c.JSON(Response[T]{
		Code:      code,
		Message:   message,
		Success:   success,
		Result:    result,
		RequestID: GetRequestID(c),
	})
}

// CustomResponse 返回自定义响应
func CustomResponseWithEpValue[T any](c *fiber.Ctx, code int, message string, success bool, result T, epValue int) error {

	// 记录响应码用于可用性统计
	c.Locals("response_code", code)
	c.Locals("response_success", success)

	return c.JSON(Response[T]{
		Code:      code,
		Message:   message,
		Success:   success,
		Result:    result,
		RequestID: GetRequestID(c),
	})
}
