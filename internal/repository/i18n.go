package repository

import (
	"context"
	"github.com/XmePlatform/i18n-service/internal/model"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// I18nRepository provides database operations using GORM
type I18nRepository struct {
	db *gorm.DB
}

func NewI18nRepository(db *gorm.DB) *I18nRepository {
	return &I18nRepository{db: db}
}

// GetTranslationsByNamespaceAndVersion gets translations for a namespace and version
func (r *I18nRepository) GetTranslationsByNamespaceAndVersion(namespace, version string) ([]model.TranslationKey, error) {
	var translationKeys []model.TranslationKey
	
	// 使用完整的字段列表确保所有语言字段都被查询
	selectFields := "tk.id, tk.key, tk.namespaceId, tk.description, tk.createdBy, tk.releaseId, tk.createdAt, tk.updatedAt, tk.zh_CN, tk.zh_TW, tk.en_US, tk.ja_JP, tk.ko_KR, tk.fr_FR, tk.de_DE, tk.es_ES, tk.pt_PT, tk.ru_RU, tk.ar_SA, tk.bn_BD, tk.hi_IN, tk.th_TH, tk.vi_VN, tk.id_ID, tk.tr_TR, tk.ur_PK, tk.fa_IR"
	
	query := r.db.Table("translation_keys tk").
		Select(selectFields).
		Joins("JOIN namespaces n ON tk.namespaceId = n.id").
		Where("n.name = ?", namespace).
		Order("tk.key")
	
	// 如果指定了版本，则加入版本条件
	if version != "" {
		query = query.Joins("LEFT JOIN releases r ON tk.releaseId = r.id").
			Where("r.version = ?", version)
	}
	
	logger.GetLogger().Info(context.Background(), "Querying translations for namespace and version", 
		zap.String("namespace", namespace), 
		zap.String("version", version))
	
	err := query.Find(&translationKeys).Error
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to query translations by namespace and version", 
			zap.Error(err))
		return nil, err
	}

	logger.GetLogger().Info(context.Background(), "Found translation keys", 
		zap.Int("count", len(translationKeys)))
	return translationKeys, nil
}

// GetTranslationsByNamespace gets translations for a namespace (env is ignored in simplified architecture)
func (r *I18nRepository) GetTranslationsByNamespace(namespace string) ([]model.TranslationKey, error) {
	var translationKeys []model.TranslationKey
	
	// 使用完整的字段列表确保所有语言字段都被查询
	selectFields := "tk.id, tk.key, tk.namespaceId, tk.description, tk.createdBy, tk.releaseId, tk.createdAt, tk.updatedAt, tk.zh_CN, tk.zh_TW, tk.en_US, tk.ja_JP, tk.ko_KR, tk.fr_FR, tk.de_DE, tk.es_ES, tk.pt_PT, tk.ru_RU, tk.ar_SA, tk.bn_BD, tk.hi_IN, tk.th_TH, tk.vi_VN, tk.id_ID, tk.tr_TR, tk.ur_PK, tk.fa_IR"
	
	logger.GetLogger().Info(context.Background(), "Querying all translations for namespace", 
		zap.String("namespace", namespace))
	
	err := r.db.Table("translation_keys tk").
		Select(selectFields).
		Joins("JOIN namespaces n ON tk.namespaceId = n.id").
		Where("n.name = ?", namespace).
		Find(&translationKeys).Error
		
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to query translations by namespace", 
			zap.Error(err))
		return nil, err
	}

	logger.GetLogger().Info(context.Background(), "Found translation keys for namespace", 
		zap.Int("count", len(translationKeys)), 
		zap.String("namespace", namespace))
	return translationKeys, nil
}

// GetTranslationKey gets a single translation key
func (r *I18nRepository) GetTranslationKey(namespace, key string) (*model.TranslationKey, error) {
	var tk model.TranslationKey
	
	// 使用完整的字段列表确保所有语言字段都被查询
	selectFields := "tk.id, tk.key, tk.namespaceId, tk.description, tk.createdBy, tk.releaseId, tk.createdAt, tk.updatedAt, tk.zh_CN, tk.zh_TW, tk.en_US, tk.ja_JP, tk.ko_KR, tk.fr_FR, tk.de_DE, tk.es_ES, tk.pt_PT, tk.ru_RU, tk.ar_SA, tk.bn_BD, tk.hi_IN, tk.th_TH, tk.vi_VN, tk.id_ID, tk.tr_TR, tk.ur_PK, tk.fa_IR"
	
	logger.GetLogger().Info(context.Background(), "Querying single translation key", 
		zap.String("key", key), 
		zap.String("namespace", namespace))
	
	err := r.db.Table("translation_keys tk").
		Select(selectFields).
		Joins("JOIN namespaces n ON tk.namespaceId = n.id").
		Where("n.name = ? AND tk.key = ?", namespace, key).
		First(&tk).Error
		
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.GetLogger().Info(context.Background(), "Translation key not found", 
				zap.String("key", key), 
				zap.String("namespace", namespace))
			return nil, nil
		}
		logger.GetLogger().Error(context.Background(), "Failed to query translation key", 
			zap.Error(err))
		return nil, err
	}

	logger.GetLogger().Info(context.Background(), "Found translation key with languages", 
		zap.String("key", key))
	return &tk, nil
}



// GetNamespaces gets all namespaces
func (r *I18nRepository) GetNamespaces() ([]model.Namespace, error) {
	var namespaces []model.Namespace
	
	// 启用SQL调试日志
	err := r.db.Debug().Order("name").Find(&namespaces).Error
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to query namespaces", 
			zap.Error(err))
		return nil, err
	}

	return namespaces, nil
}

// GetReleases gets releases for a namespace (no global releases)
func (r *I18nRepository) GetReleases(namespace string) ([]model.Release, error) {
	var releases []model.Release
	
	// 启用SQL调试日志
	query := r.db.Table("releases r").
		Select("r.*").
		Joins("JOIN namespaces n ON r.namespaceId = n.id").
		Where("n.name = ?", namespace).
		Order("r.createdAt DESC")
	
	err := query.Debug().Find(&releases).Error
		
	if err != nil {
		logger.GetLogger().Error(context.Background(), "Failed to query releases", 
			zap.Error(err))
		return nil, err
	}

	return releases, nil
}
