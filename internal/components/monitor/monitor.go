// Package monitor
// @author: demy
// @email: <EMAIL>
// @Description: promethus 数据接入
package monitor

import (
	"time"

	"github.com/XmePlatform/i18n-service/internal/pkg/metrics"
)

type Monitor struct{}

var defaultMonitor = &Monitor{}

func GetMonitor() *Monitor {
	return defaultMonitor
}

// RecordRequestDuration 记录请求耗时
func RecordRequestDuration(operation string, start time.Time) {
	duration := time.Since(start)
	metrics.RecordRequestDuration(operation, duration.Seconds())
}

// RecordDBOperationDuration 记录数据库操作耗时
func RecordDBOperationDuration(operation string, start time.Time) {
	duration := time.Since(start)
	metrics.RecordDBOperationDuration(operation, duration.Seconds())
}

// RecordCommentCreated 记录评论创建
func RecordCommentCreated() {
	metrics.RecordCommentCreated()
}

// RecordCommentDeleted 记录评论删除
func RecordCommentDeleted() {
	metrics.RecordCommentDeleted()
}

// RecordCommentLiked 记录评论点赞
func RecordCommentLiked() {
	metrics.RecordCommentLiked()
}

// RecordCommentUnliked 记录评论取消点赞
func RecordCommentUnliked() {
	metrics.RecordCommentUnliked()
}

// RecordCacheHit 记录缓存命中
func RecordCacheHit() {
	metrics.RecordCacheHit()
}

// RecordCacheMiss 记录缓存未命中
func RecordCacheMiss() {
	metrics.RecordCacheMiss()
}

// RecordStatsSyncTotal 记录统计同步总数
func RecordStatsSyncTotal(syncType string) {
	metrics.RecordStatsSyncTotal(syncType)
}

// RecordStatsSyncError 记录统计同步错误
func RecordStatsSyncError(syncType string) {
	metrics.RecordStatsSyncError(syncType)
}

// RecordStatsSyncDuration 记录统计同步耗时
func RecordStatsSyncDuration(syncType string, start time.Time) {
	duration := time.Since(start)
	metrics.RecordStatsSyncDuration(syncType, duration.Seconds())
}
