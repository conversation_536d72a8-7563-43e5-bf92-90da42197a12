package apollo

import (
	"fmt"
	"strconv"
	"sync"

	"github.com/apolloconfig/agollo/v4"
	"github.com/apolloconfig/agollo/v4/env/config"
	"github.com/apolloconfig/agollo/v4/storage"
)

const (
	DefaultNamespace = "application"
	NamespaceInfra   = "xme.infra"
)

var (
	apolloClient agollo.Client
	// 添加变更监听器相关变量
	changeListeners []func()
	listenerLock    sync.RWMutex
)

// AddChangeListener 添加配置变更监听器
func AddChangeListener(listener func()) {
	listenerLock.Lock()
	defer listenerLock.Unlock()
	changeListeners = append(changeListeners, listener)
}

// 实现 apollo.ChangeListener 接口
type changeListener struct{}

func (l *changeListener) OnChange(changeEvent *storage.ChangeEvent) {
	listenerLock.RLock()
	defer listenerLock.RUnlock()

	// 通知所有监听器
	for _, listener := range changeListeners {
		listener()
	}
}

func (l *changeListener) OnNewestChange(changeEvent *storage.FullChangeEvent) {
	// 可以根据需要实现完整变更事件的处理
}

type ApolloConfig struct {
	AppID         string
	Cluster       string
	NamespaceName string
	IP            string
	Secret        string
	IsBackup      bool
}

func Init(apolloCfg *ApolloConfig) {
	// 初始化 Apollo 客户端
	c := &config.AppConfig{
		AppID:          apolloCfg.AppID,
		Cluster:        apolloCfg.Cluster,
		IP:             apolloCfg.IP,
		NamespaceName:  apolloCfg.NamespaceName,
		Secret:         apolloCfg.Secret,
		IsBackupConfig: apolloCfg.IsBackup,
	}

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		panic("Failed to init apollo client " + err.Error())
	}
	apolloClient = client

	// 注册配置变更监听器
	apolloClient.AddChangeListener(&changeListener{})
}

func GetStringValue(key string, def string, namespace ...string) string {
	if apolloClient == nil {
		return def
	}

	ns := DefaultNamespace
	if len(namespace) > 0 {
		ns = namespace[0]
	}

	config := apolloClient.GetConfigCache(ns)
	if config == nil {
		return def
	}

	val, err := config.Get(key)
	if err != nil {
		return def
	}

	// 处理不同的值类型
	switch v := val.(type) {
	case string:
		if v == "" {
			return def
		}
		return v
	case nil:
		return def
	default:
		// 其他类型尝试转换为字符串
		return fmt.Sprintf("%v", v)
	}
}

func GetIntValue(key string, def int, namespace ...string) int {
	val := GetStringValue(key, strconv.Itoa(def), namespace...)
	intVal, err := strconv.Atoi(val)
	if err != nil {
		return def
	}
	return intVal
}

func GetBoolValue(key string, def bool, namespace ...string) bool {
	val := GetStringValue(key, strconv.FormatBool(def), namespace...)
	boolVal, err := strconv.ParseBool(val)
	if err != nil {
		return def
	}
	return boolVal
}
