package env

import (
	"log"
	"os"
	"path/filepath"
)

var (
	envRootPath = ""
)

// SetRootPath 设置应用的根目录
func SetRootPath(rootPath string) {
	if envRootPath != "" && rootPath != envRootPath {
		panic("app root path cannot set twice")
	}
	envRootPath = rootPath
	log.Printf("[env] SetRootPath=%s\n", rootPath)
}

// RootPath 返回应用的根目录
func RootPath() string {
	if envRootPath == "" {
		if os.Getenv("GDP_ROOT_PATH") != "" {
			SetRootPath(os.Getenv("GDP_ROOT_PATH"))
		} else {
			SetRootPath(detectRootPath())
		}
	}
	return envRootPath
}

// detectRootPath 自动寻找rootpath
func detectRootPath() string {
	pwd, err := os.Getwd()
	if err != nil {
		panic("DefaultApp can't get current directory: " + err.Error())
	}
	var dir string
	defer func() {
		log.Printf("[env] auto detect rootPath= %s\n", dir)
	}()

	bindir := filepath.Dir(os.Args[0])
	if !filepath.IsAbs(bindir) {
		bindir = filepath.Join(pwd, bindir)
	}
	// 如果有和可执行文件平级的conf目录，则当前目录就是根目录
	// 这通常是直接在代码目录里go build然后直接执行生成的结果
	dir = filepath.Join(bindir, "conf")
	if _, err := os.Stat(dir); !os.IsNotExist(err) {
		dir = bindir
		return dir
	}
	// 如果有和可执行文件上级平级的conf目录，则上层目录就是根目录
	// 这一般是按标准进行部署
	dir = filepath.Join(filepath.Dir(bindir), "conf")
	if _, err := os.Stat(dir); !os.IsNotExist(err) {
		dir = filepath.Dir(bindir)
		return dir
	}
	// 如果都没有，但可执行文件的父目录名称为bin，则bin的上一层就是根目录
	// 这种情况适用于配置目录名为：etc, config, configs等情况
	if filepath.Base(bindir) == "bin" {
		dir = filepath.Dir(bindir)
		return dir
	}
	// 如果都不符合，当前路径就是根目录，这一般是使用go run main.go的情况
	dir = pwd
	return dir
}
