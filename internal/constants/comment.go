package constants

// 评论来源
const (
	SourceUser = "user"
	SourceAI   = "ai"
)

// 评论状态
const (
	StatusPending  int8 = 0 // 待审核
	StatusApproved int8 = 1 // 已通过
	StatusRejected int8 = 2 // 已拒绝
)

const (
	CommentSourceTypeNormal = 0
	CommentSourceTypeAI     = 1
)

// 分页默认值
const (
	DefaultPageSize = 20
	MaxPageSize     = 100
)

// MaxCommentLength 评论最大字符数
const (
	MaxCommentLength = 300
)

const (
	CommentTypeComment = 1
	CommentTypeReply   = 2
)

// 举报状态
const (
	ReportStatusPending  int8 = 0 // 待处理
	ReportStatusApproved int8 = 1 // 已处理
	ReportStatusRejected int8 = 2 // 已驳回
)

// 举报类型
const (
	ReportTypePolitical  int8 = 1  // 政治类
	ReportTypeViolence   int8 = 2  // 暴恐类
	ReportTypeSlander    int8 = 3  // 诽谤类
	ReportTypePorn       int8 = 4  // 色情类
	ReportTypeVulgar     int8 = 5  // 低俗类
	ReportTypeFraud      int8 = 6  // 诈骗类
	ReportTypeGambling   int8 = 7  // 赌博类
	ReportTypePrivacy    int8 = 8  // 侵权类
	ReportTypeUnfriendly int8 = 9  // 不友善
	ReportTypeOther      int8 = 10 // 其他
)

// 举报原因
const (
	ReportReasonPolitical  = "political"  // 政治类
	ReportReasonViolence   = "violence"   // 暴恐类
	ReportReasonSlander    = "slander"    // 诽谤类
	ReportReasonPorn       = "porn"       // 色情类
	ReportReasonVulgar     = "vulgar"     // 低俗类
	ReportReasonFraud      = "fraud"      // 诈骗类
	ReportReasonGambling   = "gambling"   // 赌博类
	ReportReasonPrivacy    = "privacy"    // 侵权类
	ReportReasonUnfriendly = "unfriendly" // 不友善
	ReportReasonOther      = "other"      // 其他
)

// 举报对象类型
const (
	ReportTargetTypeComment = "comment"
	ReportTargetTypeReply   = "reply"
)

// i18n message keys
const (
	// Comment messages
	CommentContentEmpty   = "comment.create.empty"
	CommentContentTooLong = "comment.create.too_long"
	CommentGetSuccess     = "comment.get.success"
	CommentGetError       = "comment.get.error"
	CommentAlreadyLiked   = "comment.like.already"
	CommentNotLikedYet    = "comment.like.not_yet"
)
