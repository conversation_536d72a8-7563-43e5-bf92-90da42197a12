package constants

import "time"

const (
	// Redis key 前缀
	RedisKeyComment             = "comment:"
	RedisKeyReply               = "reply:"
	RedisKeyCommentSet          = "comment:set:"
	RedisKeyReplySet            = "reply:set:"
	RedisKeyCommentContent      = "comment:content:"
	RedisKeyCommentReply        = "comment:replies:"
	RedisKeyCommentLike         = "like:set:" // 新的点赞 Set 前缀
	RedisKeyContentCommentCount = "content:comment:count:"

	// 缓存过期时间
	RedisCommentCacheExpiration = 24 * time.Hour * 7  // 30天
	RedisLikeCacheExpiration    = 30 * time.Hour * 24 // 7天
)
