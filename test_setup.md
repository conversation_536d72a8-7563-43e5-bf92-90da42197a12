# Go i18n Service 测试环境设置

## 快速测试流程

### 1. 环境配置

你的Go服务支持两种测试模式：

**模式A：指定版本号**
```bash
curl "http://localhost:8080/api/v1/translations/auth?version=v1.0.0&lang=zh_CN"
```

**模式B：使用环境自动版本（推荐）**
```bash
curl "http://localhost:8080/api/v1/translations/auth?env=TEST&lang=zh_CN"
```

### 2. 测试环境不需要指定版本号

**答案：不需要！**

你可以直接使用环境参数：
- `env=DEV` - 开发环境
- `env=TEST` - 测试环境  
- `env=pre` - 预发布环境
- `env=PROD` - 生产环境

系统会自动查询该环境当前部署的版本。

### 3. 实际测试步骤

1. **启动Go服务**
   ```bash
   cd /opt/projects/i18n/i18n-service
   go run cmd/server/main_new.go
   ```

2. **运行测试脚本**
   ```bash
   chmod +x test_api.sh
   ./test_api.sh
   ```

3. **手动测试关键API**
   ```bash
   # 健康检查
   curl http://localhost:8080/health
   
   # 测试环境翻译（无需版本号）
   curl "http://localhost:8080/api/v1/translations/auth?env=TEST&lang=zh_CN"
   
   # 获取当前测试环境版本
   curl "http://localhost:8080/api/v1/versions/auth/current?env=TEST"
   ```

### 4. Java客户端测试

```java
// 简单调用 - 自动使用TEST环境当前版本
String translation = i18nClient.getTranslation("auth", "login.welcome", "zh_CN");

// 或者明确指定环境
String translation = i18nClient.getTranslationByEnv("auth", "login.welcome", "zh_CN", "TEST");
```

### 5. 数据库要求

确保你的数据库中有：
- Namespace表中有'auth'命名空间
- TranslationKey表中有翻译键数据
- Release表中有版本数据
- Deployment表中有部署记录（关联环境和版本）

### 6. 优势

✅ **测试简单**：只需指定环境，无需记住版本号  
✅ **自动管理**：版本切换在管理后台操作，API调用不变  
✅ **降级友好**：API失败时返回key本身，不会破坏业务  
✅ **缓存优化**：相同环境的请求会被缓存，提高性能

这样的设计让你的测试流程更加简洁和实用！
