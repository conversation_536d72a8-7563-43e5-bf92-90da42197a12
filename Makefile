.PHONY: build run test clean docker-build docker-run docker-stop deps fmt lint

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=i18n-service
BINARY_UNIX=$(BINARY_NAME)_unix

# Build the application
build:
	$(GOBUILD) -o $(BINARY_NAME) -v cmd/server/main.go

# Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v cmd/server/main.go

# Run the application
run:
	$(GOBUILD) -o $(BINARY_NAME) -v cmd/server/main.go
	./$(BINARY_NAME)

# Run with hot reload (requires air: go install github.com/cosmtrek/air@latest)
dev:
	air

# Test
test:
	$(GOTEST) -v ./...

# Test with coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)
	rm -f coverage.out coverage.html

# Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
fmt:
	$(GOCMD) fmt ./...

# Lint code (requires golangci-lint)
lint:
	golangci-lint run

# Docker commands
docker-build:
	docker build -t $(BINARY_NAME):latest .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f i18n-service

# Database migration (if using migrate tool)
migrate-up:
	migrate -path migrations -database "mysql://root:password@tcp(localhost:3306)/i18n" up

migrate-down:
	migrate -path migrations -database "mysql://root:password@tcp(localhost:3306)/i18n" down

# Load test (requires hey: go install github.com/rakyll/hey@latest)
load-test:
	hey -n 1000 -c 10 http://localhost:8080/health

# Benchmark specific endpoint
benchmark-translations:
	hey -n 1000 -c 10 http://localhost:8080/api/v1/translations/common

# Check service health
health-check:
	curl -f http://localhost:8080/health || exit 1

# View metrics
metrics:
	curl http://localhost:8080/metrics

# Install development tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/rakyll/hey@latest
	go install -tags 'mysql' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
