package main

import (
	"fmt"
	"github.com/XmePlatform/i18n-service/internal/components/apollo"
	"github.com/XmePlatform/i18n-service/internal/middleware"
	"github.com/XmePlatform/i18n-service/internal/pkg/logger"
	"github.com/XmePlatform/i18n-service/internal/pkg/metrics"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/adaptor"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/XmePlatform/i18n-service/internal/cache"
	"github.com/XmePlatform/i18n-service/internal/config"
	"github.com/XmePlatform/i18n-service/internal/database"
	"github.com/XmePlatform/i18n-service/internal/handler"
	"github.com/XmePlatform/i18n-service/internal/repository"
	"github.com/XmePlatform/i18n-service/internal/service"
)

func main() {
	// 设置日志格式

	apolloCfg := &apollo.ApolloConfig{
		AppID:         os.Getenv("APOLLO_APP_ID"),
		Cluster:       os.Getenv("APOLLO_CLUSTER"),
		NamespaceName: os.Getenv("APOLLO_NAMESPACE"),
		IP:            os.Getenv("APOLLO_META"),
		Secret:        os.Getenv("APOLLO_SECRET"),
		IsBackup:      true,
	}

	err := config.InitConfig(apolloCfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to init config: %v", err))
	}

	err = logger.InitLogger(apollo.GetStringValue("log.env", "prod"))
	if err != nil {
		panic(fmt.Sprintf("Failed to init logger: %v", err))
	}

	logger.InitMysqlLogger()

	cfg := config.GetConfig()
	appLogger := logger.GetLogger()
	defer logger.Sync()

	// 连接数据库
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 初始化缓存管理器
	err = cache.InitCache(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize cache: %v", err)
	}
	defer func() {
		if cacheManager := cache.GetCacheManager(); cacheManager != nil {
			cacheManager.Close()
		}
	}()

	// 初始化仓库层
	i18nRepo := repository.NewI18nRepository(db.DB)

	// 初始化服务层
	i18nService := service.NewI18nService(i18nRepo)

	// 初始化处理器
	i18nHandler := handler.NewI18nHandler(i18nService)

	appConfig := fiber.Config{
		ReadBufferSize: 16 * 1024,
		ReadTimeout:    5 * time.Second,
		WriteTimeout:   10 * time.Second,
		IdleTimeout:    120 * time.Second,
		ProxyHeader:    fiber.HeaderXForwardedFor,
	}
	app := fiber.New(appConfig)

	// 添加中间件
	app.Use(recover.New())
	app.Use(middleware.RequestID())
	app.Use(middleware.PrometheusMiddleware())
	app.Use(middleware.AvailabilityMiddleware(appLogger)) // 添加可用性监控中间件

	//初始化promethus
	gatherer := metrics.InitPromethus()
	// 添加 Prometheus 指标接口
	app.Get("/metrics", adaptor.HTTPHandler(promhttp.HandlerFor(gatherer, promhttp.HandlerOpts{})))

	// 添加健康检查接口
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.SendString("OK")
	})

	app.Get("/readiness", func(c *fiber.Ctx) error {
		return c.SendString("OK")
	})

	app.Get("/liveness", func(c *fiber.Ctx) error {
		return c.SendString("OK")
	})

	// API路由组
	api := app.Group("/i18n/api/v1")

	// 翻译相关路由
	api.Get("/translations/:namespace", i18nHandler.GetTranslations)
	api.Get("/translations/:namespace/key/:key", i18nHandler.GetTranslationKey)

	// 版本管理路由
	api.Get("/releases/:namespace", i18nHandler.GetReleases)

	// 命名空间路由
	api.Get("/namespaces", i18nHandler.GetNamespaces)

	// 缓存管理路由（只保留实际需要的精确缓存失效操作）
	cache := api.Group("/cache")
	// 主要API：按namespace和version精确失效缓存（前端数据变更时调用）
	cache.Post("/namespace/:namespace/clear", i18nHandler.InvalidateNamespaceVersionCache) // 支持?version=xxx参数
	// 版本状态变更API：只清空版本列表缓存（版本发布时调用）
	cache.Post("/releases/:namespace/clear", i18nHandler.InvalidateReleaseListCache)

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-quit
		logger.GetConsoleLogger().Info("Shutting down server...")
		if err := app.Shutdown(); err != nil {
			logger.GetConsoleLogger().Fatal("Server forced to shutdown:", zap.Error(err))
		}
	}()

	// 启动服务器
	if err := app.Listen(":8080"); err != nil {
		logger.GetConsoleLogger().Fatal("Failed to start server", zap.Error(err))
	}
}
