# i18n Service API 文档

基于简化架构的Go i18n服务API设计，支持版本化翻译管理。

## 架构概述

- **Namespace**: 功能模块分组（如：auth, user, order）
- **Release**: 版本管理，支持命名空间版本和全局版本
- **TranslationKey**: 翻译键，支持19种语言
- **Environment**: 部署环境（DEV, TEST, pre, PROD）
- **Deployment**: 版本部署记录

## API 端点

### 1. 获取翻译内容

#### 获取命名空间的所有翻译
```http
GET /api/v1/translations/{namespace}?version=v1.0.0&lang=zh_CN&env=PROD
```

**参数说明:**
- `namespace`: 命名空间名称（必需）
- `version`: 版本号（可选，如果不指定且提供env，则使用当前环境默认版本）
- `lang`: 语言代码（可选，默认返回所有语言）
- `env`: 环境名称（可选，用于获取当前部署版本）

**响应示例:**
```json
{
  "namespace": "auth",
  "version": "v1.0.0",
  "translations": {
    "login.welcome": "欢迎登录",
    "login.username": "用户名",
    "login.password": "密码"
  },
  "metadata": {
    "count": 3,
    "lastUpdated": "2024-01-01T12:00:00Z",
    "cacheHit": false
  }
}
```

#### 获取单个翻译键
```http
GET /api/v1/translations/{namespace}/key/{key}?version=v1.0.0&lang=zh_CN
```

**响应示例:**
```json
{
  "namespace": "auth",
  "key": "login.welcome",
  "version": "v1.0.0",
  "language": "zh_CN",
  "value": "欢迎登录"
}
```

### 2. 版本管理

#### 获取当前环境版本
```http
GET /api/v1/versions/{namespace}/current?env=PROD
```

**响应示例:**
```json
{
  "namespace": "auth",
  "environment": "PROD",
  "version": "v1.0.0"
}
```

#### 获取版本列表
```http
GET /api/v1/releases/{namespace}?env=PROD
```

**响应示例:**
```json
[
  {
    "id": "rel_123",
    "version": "v1.0.0",
    "description": "初始版本",
    "status": "PUBLISHED",
    "namespaceId": "ns_auth",
    "createdAt": "2024-01-01T12:00:00Z",
    "namespace": {
      "name": "auth",
      "displayName": "认证模块"
    }
  }
]
```

### 3. 命名空间管理

#### 获取所有命名空间
```http
GET /api/v1/namespaces
```

**响应示例:**
```json
[
  {
    "id": "ns_auth",
    "name": "auth",
    "displayName": "认证模块",
    "description": "用户认证相关翻译",
    "createdAt": "2024-01-01T12:00:00Z"
  }
]
```

### 4. 健康检查

```http
GET /health
```

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": **********,
  "version": "1.0.0",
  "database": "ok"
}
```

## 支持的语言代码

| 语言 | 代码 | 别名 |
|------|------|------|
| 简体中文 | zh_CN | zh-CN |
| 繁体中文 | zh_TW | zh-TW |
| 英语(美国) | en_US | en-US, en |
| 日语 | ja_JP | ja-JP, ja |
| 韩语 | ko_KR | ko-KR, ko |
| 法语 | fr_FR | fr-FR, fr |
| 德语 | de_DE | de-DE, de |
| 西班牙语 | es_ES | es-ES, es |
| 葡萄牙语 | pt_PT | pt-PT, pt |
| 俄语 | ru_RU | ru-RU, ru |
| 阿拉伯语 | ar_SA | ar-SA, ar |
| 孟加拉语 | bn_BD | bn-BD, bn |
| 印地语 | hi_IN | hi-IN, hi |
| 泰语 | th_TH | th-TH, th |
| 越南语 | vi_VN | vi-VN, vi |
| 印尼语 | id_ID | id-ID, id |
| 土耳其语 | tr_TR | tr-TR, tr |
| 乌尔都语 | ur_PK | ur-PK, ur |
| 意大利语 | it_IT | it-IT, it |

## Java客户端使用示例

### 1. 基本配置

```yaml
# application.yml
i18n:
  service:
    url: "http://i18n-service:8080"
    timeout: 5000
  cache:
    enabled: true
    ttl: 300 # 5分钟
  default:
    namespace: "user"
    language: "zh_CN"
    environment: "PROD"
```

### 2. Java客户端代码

```java
@Service
public class I18nClient {
    
    @Value("${i18n.service.url}")
    private String serviceUrl;
    
    @Value("${i18n.default.environment}")
    private String defaultEnv;
    
    private final RestTemplate restTemplate;
    private final RedisTemplate<String, String> redisTemplate;
    
    /**
     * 获取翻译（推荐方式 - 使用当前环境默认版本）
     */
    public String getTranslation(String namespace, String key, String lang) {
        String cacheKey = String.format("i18n:%s:%s:%s", namespace, key, lang);
        
        // 先从缓存获取
        String cached = redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 从服务获取
        String url = String.format("%s/api/v1/translations/%s/key/%s?env=%s&lang=%s", 
            serviceUrl, namespace, key, defaultEnv, lang);
            
        try {
            ResponseEntity<TranslationResponse> response = restTemplate.getForEntity(url, TranslationResponse.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String value = response.getBody().getValue();
                // 缓存5分钟
                redisTemplate.opsForValue().set(cacheKey, value, Duration.ofMinutes(5));
                return value;
            }
        } catch (Exception e) {
            log.warn("Failed to get translation for {}:{}, fallback to key", namespace, key, e);
        }
        
        return key; // 降级返回key本身
    }
    
    /**
     * 获取指定版本的翻译
     */
    public String getTranslation(String namespace, String key, String lang, String version) {
        String url = String.format("%s/api/v1/translations/%s/key/%s?version=%s&lang=%s", 
            serviceUrl, namespace, key, version, lang);
            
        try {
            ResponseEntity<TranslationResponse> response = restTemplate.getForEntity(url, TranslationResponse.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getValue();
            }
        } catch (Exception e) {
            log.warn("Failed to get translation for {}:{} version {}", namespace, key, version, e);
        }
        
        return key;
    }
    
    /**
     * 批量获取翻译
     */
    public Map<String, String> getTranslations(String namespace, String lang) {
        String url = String.format("%s/api/v1/translations/%s?env=%s&lang=%s", 
            serviceUrl, namespace, defaultEnv, lang);
            
        try {
            ResponseEntity<TranslationBatchResponse> response = restTemplate.getForEntity(url, TranslationBatchResponse.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getTranslations();
            }
        } catch (Exception e) {
            log.warn("Failed to get translations for namespace {}", namespace, e);
        }
        
        return Collections.emptyMap();
    }
    
    /**
     * 获取当前版本
     */
    public String getCurrentVersion(String namespace) {
        String url = String.format("%s/api/v1/versions/%s/current?env=%s", 
            serviceUrl, namespace, defaultEnv);
            
        try {
            ResponseEntity<VersionResponse> response = restTemplate.getForEntity(url, VersionResponse.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody().getVersion();
            }
        } catch (Exception e) {
            log.warn("Failed to get current version for namespace {}", namespace, e);
        }
        
        return "latest";
    }
}
```

### 3. 使用示例

```java
@RestController
public class UserController {
    
    @Autowired
    private I18nClient i18nClient;
    
    @GetMapping("/login")
    public ResponseEntity<LoginResponse> login() {
        // 获取当前环境的翻译
        String welcomeMsg = i18nClient.getTranslation("auth", "login.welcome", "zh_CN");
        String usernameLabel = i18nClient.getTranslation("auth", "login.username", "zh_CN");
        
        // 或者批量获取
        Map<String, String> authTranslations = i18nClient.getTranslations("auth", "zh_CN");
        
        return ResponseEntity.ok(LoginResponse.builder()
            .welcomeMessage(welcomeMsg)
            .usernameLabel(usernameLabel)
            .translations(authTranslations)
            .build());
    }
}
```

## 部署和运维

### 1. 版本发布流程

1. **开发阶段**: 在管理后台创建DRAFT版本，添加翻译内容
2. **测试阶段**: 发布版本到TEST环境进行测试
3. **预发布**: 发布到pre环境进行最终验证
4. **生产发布**: 发布到PROD环境
5. **回滚支持**: 如有问题可快速回滚到之前版本

### 2. 缓存策略

- **应用层缓存**: Redis缓存翻译内容，TTL 5分钟
- **服务层缓存**: Go服务内存缓存，TTL 10分钟
- **版本缓存**: 当前版本信息缓存，TTL 5分钟

### 3. 监控指标

- API响应时间
- 缓存命中率
- 翻译缺失率
- 版本切换频率

这个设计提供了完整的版本化翻译管理能力，支持灵活的部署策略和高性能的翻译查询。
