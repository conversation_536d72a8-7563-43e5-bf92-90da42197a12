*.exe
*.dll
*.so
*.dylib
*.o
*.a
*.out

# Go 模块和依赖缓存

# 包管理生成的目录
/vendor/

# 编译临时文件
*_test.go

# 测试覆盖率报告文件
*.cover

# 日志文件
*.log

# 数据库、调试或其他临时文件
*.db
*.sqlite
*.sqlite3
*.sql
*.tmp

# 项目临时目录
tmp/
temp/

# IDE 相关文件
.idea/
.vscode/
*.iml

# 操作系统文件
.DS_Store
Thumbs.db

# Docker
docker-compose.override.yml
*.env

# 环境配置文件
.env

# 不需要的文件
node_modules/
dist/
comment-service-application.json
.cursorrules
.kiro
