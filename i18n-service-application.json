{"appId": "i18n-service", "cluster": "test", "namespaceName": "application", "releaseKey": "20250811013454-83e32d7651c268e9", "configurations": {"db.i18n": "{\n    \"host\": \"xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com\",\n    \"port\": \"3306\",\n    \"user\": \"i18n\",\n    \"password\": \"k7xetf5YKB\",\n    \"dbname\": \"i18n\"\n}", "redis.i18n": "{\n    \"host\": \"xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com\",\n    \"port\": 6379,\n    \"enabled\":true,\n   \"cluster\":true,\n   \"addrs\":[\"xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com:6379\"]\n}"}}