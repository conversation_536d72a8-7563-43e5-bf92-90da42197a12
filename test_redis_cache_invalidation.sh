#!/bin/bash

# Redis分布式缓存失效API测试脚本
# 注意：已移除所有危险的全局清空缓存操作，只保留安全的精确失效方法

BASE_URL="http://localhost:8080/api/v1"

echo "=== Redis分布式缓存失效API测试 ==="
echo "注意：已移除危险的全局清空缓存操作，确保生产环境安全"
echo

# 测试1：清空指定命名空间的翻译缓存
echo "1. 测试清空指定命名空间的翻译缓存"
curl -X POST "$BASE_URL/cache/translations/web/clear" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo

# 测试2：清空指定命名空间的版本缓存
echo "2. 测试清空指定命名空间的版本缓存"
curl -X POST "$BASE_URL/cache/versions/web/clear" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo

# 测试3：清空指定命名空间和版本的缓存（高性能版本）
echo "3. 测试清空指定命名空间的缓存（不指定版本）"
curl -X POST "$BASE_URL/cache/namespace/web/clear" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo

# 测试4：清空指定命名空间和特定版本的缓存
echo "4. 测试清空指定命名空间和特定版本的缓存"
curl -X POST "$BASE_URL/cache/namespace/web/clear?version=v1.0.0" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo

# 测试5：验证危险的全局清空操作已被移除
echo "5. 验证危险的全局清空缓存操作已被移除（应该返回404）"
curl -X POST "$BASE_URL/cache/clear" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo

echo "=== 安全性验证完成 ==="
echo "✅ 所有危险的全局清空缓存操作已被移除"
echo "✅ 只保留安全的、精确的缓存失效方法"
echo "✅ 支持按namespace和version进行精确缓存失效"
echo "✅ 避免使用Redis SCAN操作，提升性能"
echo
echo "可用的安全缓存失效API："
echo "- POST /api/v1/cache/translations/{namespace}/clear - 清空指定命名空间的翻译缓存"
echo "- POST /api/v1/cache/versions/{namespace}/clear - 清空指定命名空间的版本缓存"
echo "- POST /api/v1/cache/namespace/{namespace}/clear - 清空指定命名空间的缓存"
echo "- POST /api/v1/cache/namespace/{namespace}/clear?version={version} - 清空指定命名空间和版本的缓存"
