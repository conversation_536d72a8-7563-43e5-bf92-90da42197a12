#!/bin/bash

# 测试统一响应格式的 i18n API
# 所有响应都应该包含 code, message, success, result 字段

BASE_URL="http://localhost:8080/i18n/api/v1"

echo "=== i18n Service API Test (Unified Response Format, No Global Versions) ==="
echo "Testing all endpoints with unified JSON response format"
echo "Note: All releases are now namespace-specific, no global versions supported"
echo "Base URL: $BASE_URL"
echo "=========================================="

# 测试健康检查
echo "1. Testing Health Check..."
curl -s "$BASE_URL/../health" | jq '.'
echo ""

# 测试获取命名空间列表
echo "2. Testing Get Namespaces..."
response=$(curl -s "$BASE_URL/namespaces")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: [...]}"
echo ""

# 测试获取翻译内容 (使用环境参数)
echo "3. Testing Get Translations by Environment..."
response=$(curl -s "$BASE_URL/translations/common?env=DEV")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: {...}}"
echo ""

# 测试获取翻译内容 (使用版本参数)
echo "4. Testing Get Translations by Version..."
response=$(curl -s "$BASE_URL/translations/common?version=v1.0.0")
echo "$response" | jq '.'
echo ""

# 测试获取翻译内容 (指定语言)
echo "5. Testing Get Translations with Language..."
response=$(curl -s "$BASE_URL/translations/common?lang=zh_CN&env=DEV")
echo "$response" | jq '.'
echo ""

# 测试获取单个翻译键
echo "6. Testing Get Single Translation Key..."
response=$(curl -s "$BASE_URL/translations/common/key/welcome_message?lang=en_US")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: {namespace, key, language, value}}"
echo ""

# 测试获取当前版本
echo "7. Testing Get Current Version..."
response=$(curl -s "$BASE_URL/versions/common/current?env=DEV")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: {namespace, environment, version}}"
echo ""

# 测试获取版本列表 (只返回该namespace的版本，无全局版本)
echo "8. Testing Get Releases..."
response=$(curl -s "$BASE_URL/releases/common")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: [namespace-specific releases only]}"
echo ""

# 测试清除缓存
echo "9. Testing Clear Cache..."
response=$(curl -s -X POST "$BASE_URL/cache/clear")
echo "$response" | jq '.'
echo "Expected format: {code: 200, message: \"success\", success: true, result: {message: \"Cache cleared successfully\"}}"
echo ""

# 测试错误情况 - 缺少必需参数
echo "10. Testing Error Response - Missing Namespace..."
response=$(curl -s "$BASE_URL/translations/")
echo "$response" | jq '.'
echo "Expected format: {code: 400, message: \"...\", success: false, result: null}"
echo ""

# 测试错误情况 - 不存在的命名空间
echo "11. Testing Error Response - Non-existent Namespace..."
response=$(curl -s "$BASE_URL/translations/nonexistent")
echo "$response" | jq '.'
echo "Expected format: {code: 500, message: \"...\", success: false, result: null}"
echo ""

echo "=========================================="
echo "API Response Format Validation Complete!"
echo ""
echo "All responses should follow this format:"
echo "{"
echo "  \"code\": 200,"
echo "  \"message\": \"success\","
echo "  \"success\": true,"
echo "  \"result\": {...},"
echo "  \"request_id\": \"...\""
echo "}"
echo ""
echo "Error responses should follow this format:"
echo "{"
echo "  \"code\": 400/500,"
echo "  \"message\": \"error description\","
echo "  \"success\": false,"
echo "  \"result\": null,"
echo "  \"request_id\": \"...\""
echo "}"
