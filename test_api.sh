#!/bin/bash

# Go i18n Service API 测试脚本

BASE_URL="http://localhost:8080"

echo "🚀 测试 Go i18n Service API (简化版)"
echo "================================"

# 启动说明
echo "📋 启动服务："
echo "   cd /opt/projects/i18n/i18n-service"
echo "   go run cmd/server/simple_main.go"
echo ""

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq '.' || echo "❌ 健康检查失败"
echo ""

# 2. 获取命名空间列表
echo "2. 获取命名空间列表..."
curl -s "$BASE_URL/api/v1/namespaces" | jq '.' || echo "❌ 获取命名空间失败"
echo ""

# 3. 获取翻译内容（使用环境默认版本）- 推荐方式
echo "3. 获取翻译（TEST环境自动版本）..."
curl -s "$BASE_URL/api/v1/translations/auth?env=TEST&lang=zh_CN" | jq '.' || echo "❌ 获取翻译失败"
echo ""

# 4. 获取翻译内容（指定版本）
echo "4. 获取翻译（指定版本）..."
curl -s "$BASE_URL/api/v1/translations/auth?version=v1.0.0&lang=zh_CN" | jq '.' || echo "❌ 获取指定版本翻译失败"
echo ""

# 5. 获取单个翻译键
echo "5. 获取单个翻译键..."
curl -s "$BASE_URL/api/v1/translations/auth/key/login.welcome?lang=zh_CN" | jq '.' || echo "❌ 获取单个翻译键失败"
echo ""

# 6. 获取当前版本
echo "6. 获取当前版本..."
curl -s "$BASE_URL/api/v1/versions/auth/current?env=TEST" | jq '.' || echo "❌ 获取当前版本失败"
echo ""

# 7. 获取版本列表
echo "7. 获取版本列表..."
curl -s "$BASE_URL/api/v1/releases/auth" | jq '.' || echo "❌ 获取版本列表失败"
echo ""

# 8. 测试不同环境
echo "8. 测试不同环境..."
echo "   DEV环境:"
curl -s "$BASE_URL/api/v1/translations/auth?env=DEV&lang=zh_CN" | jq '.environment' || echo "❌ DEV环境失败"
echo "   PROD环境:"
curl -s "$BASE_URL/api/v1/translations/auth?env=PROD&lang=zh_CN" | jq '.environment' || echo "❌ PROD环境失败"
echo ""

# 9. 清除缓存测试
echo "9. 清除缓存..."
curl -s -X POST "$BASE_URL/api/v1/cache/clear" | jq '.' || echo "❌ 清除缓存失败"
echo ""

echo "✅ 测试完成！"
echo ""
echo "💡 核心特性："
echo "   ✅ 环境自动版本：?env=TEST（推荐）"
echo "   ✅ 指定版本：?version=v1.0.0"
echo "   ✅ 降级友好：API失败返回key本身"
echo "   ✅ 缓存优化：10分钟缓存提升性能"
echo ""
echo "🔧 数据库要求："
echo "   - 设置 DATABASE_URL 环境变量"
echo "   - 或修改 simple_main.go 中的默认连接字符串"
